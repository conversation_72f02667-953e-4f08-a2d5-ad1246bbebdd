<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang_pagetsconfig.xlf" date="2023-01-19T14:41:00Z" product-name="cms" target-language="de">
    <header/>
    <body>
      <trans-unit id="module.pagetsconfig.title" resname="module.pagetsconfig.title" approved="yes">
        <source>Page TSconfig</source>
        <target state="final">Page TSconfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig.description" resname="module.pagetsconfig.description" approved="yes">
        <source>The page TSconfig module lets you manage configuration overrides for editors and option display throughout the Backend.</source>
        <target state="final">Mit dem Page TSconfig Modul können Sie Konfigurationsüberschreibungen für Redakteure und die Anzeige von Optionen im gesamten Backend verwalten.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig.shortDescription" resname="module.pagetsconfig.shortDescription" approved="yes">
        <source>Page TSconfig tools</source>
        <target state="final">Page TSconfig Tools</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig.noAccess" resname="module.pagetsconfig.noAccess" approved="yes">
        <source>Access to this module denied.</source>
        <target state="final">Zugriff auf dieses Modul verweigert.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages" resname="module.pagetsconfig_pages" approved="yes">
        <source>Pages containing page TSconfig</source>
        <target state="final">Seiten, die Page TSconfig enthalten</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages.headline" resname="module.pagetsconfig_pages.headline" approved="yes">
        <source>Pages containing page TSconfig</source>
        <target state="final">Seiten, die Page TSconfig enthalten</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages.description" resname="module.pagetsconfig_pages.description" approved="yes">
        <source>Global overview of all pages in the database containing page TSconfig.</source>
        <target state="final">Globale Übersicht aller Seiten in der Datenbank, die benutzerdefiniertes Page TSconfig enthalten.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages.noRecords" resname="module.pagetsconfig_pages.noRecords" approved="yes">
        <source>There are no pages containing page TSconfig settings in the page configuration.</source>
        <target state="final">Es gibt keine Seiten mit Page TSconfig-Einstellungen in der Seitenkonfiguration.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages.pagetitle" resname="module.pagetsconfig_pages.pagetitle" approved="yes">
        <source>Page title</source>
        <target state="final">Seitentitel</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_pages.written_tsconfig_lines" resname="module.pagetsconfig_pages.written_tsconfig_lines" approved="yes">
        <source>Number of lines of code in field "TSconfig"</source>
        <target state="final">Anzahl der Codezeilen im Feld "TSconfig"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active" resname="module.pagetsconfig_active" approved="yes">
        <source>Active page TSconfig</source>
        <target state="final">Aktive Page TSconfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.headline" resname="module.pagetsconfig_active.headline" approved="yes">
        <source>Active page TSconfig of page "%s"</source>
        <target state="final">Aktive Page TSconfig auf Seite "%s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.description" resname="module.pagetsconfig_active.description" approved="yes">
        <source>Overview of the current page TSconfig of the system. A targeted search for values and names is possible. The list of results can also be filtered according to conditions.</source>
        <target state="final">Übersicht des aktuell geladenen Page TSconfig dieses Systems. Eine gezielte Suche nach Werten und Namen ist möglich. Die Ergebnisliste kann auch nach Bedingungen gefiltert werden.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.displayConstantSubstitutions" resname="module.pagetsconfig_active.displayConstantSubstitutions" approved="yes">
        <source>Substitute constants in page TSconfig</source>
        <target state="final">Konstanten in Page TSconfig ersetzen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.displayComments" resname="module.pagetsconfig_active.displayComments" approved="yes">
        <source>Display comments</source>
        <target state="final">Kommentare anzeigen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.sortAlphabetically" resname="module.pagetsconfig_active.sortAlphabetically" approved="yes">
        <source>Sort keys alphabetically</source>
        <target state="final">Schlüssel alphabetisch sortieren</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.header.conditions" resname="module.pagetsconfig_active.panel.header.conditions" approved="yes">
        <source>Conditions</source>
        <target state="final">Bedingungen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.header.numberOfSearchMatches" resname="module.pagetsconfig_active.panel.header.numberOfSearchMatches" approved="yes">
        <source>%s search match(es)</source>
        <target state="final">%s Suchergebnis(se)</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.info.conditionActiveCount.multiple" resname="module.pagetsconfig_active.panel.info.conditionActiveCount.multiple" approved="yes">
        <source>%s active conditions</source>
        <target state="final">%s aktive Bedingungen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.info.conditionActiveCount.single" resname="module.pagetsconfig_active.panel.info.conditionActiveCount.single" approved="yes">
        <source>%s active condition</source>
        <target state="final">%s aktive Bedingung</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.info.conditionWithConstant" resname="module.pagetsconfig_active.panel.info.conditionWithConstant" approved="yes">
        <source>Site settings usage: [%s]</source>
        <target state="final">Site Settings Nutzung: [%s]</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.panel.header.configuration" resname="module.pagetsconfig_active.panel.header.configuration" approved="yes">
        <source>Configuration</source>
        <target state="final">Konfiguration</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.siteSettings" resname="module.pagetsconfig_active.siteSettings" approved="yes">
        <source>Constants from site settings</source>
        <target state="final">Konstanten aus der Site-Konfiguration</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.activePageTsConfig" resname="module.pagetsconfig_active.activePageTsConfig" approved="yes">
        <source>Page TSconfig</source>
        <target state="final">Page TSconfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.noPageTSconfigAvailable" resname="module.pagetsconfig_active.noPageTSconfigAvailable" approved="yes">
        <source>No suitable page TSconfig available.</source>
        <target state="final">Keine passende Page TSconfig verfügbar.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_active.tree.valueWithConstant" resname="module.pagetsconfig_active.tree.valueWithConstant" approved="yes">
        <source>Constant usage: [%s]</source>
        <target state="final">Konstanten-Nutzung: [%s]</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes" resname="module.pagetsconfig_includes" approved="yes">
        <source>Included page TSconfig</source>
        <target state="final">Eingebundene Page TSconfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.headline" resname="module.pagetsconfig_includes.headline" approved="yes">
        <source>Included page TSconfig of page "%s"</source>
        <target state="final">Eingebundene Page TSconfig auf der Seite "%s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.description" resname="module.pagetsconfig_includes.description" approved="yes">
        <source>Overview of the included TypoScript and include order for the current page.</source>
        <target state="final">Übersicht eingebundener TypoScript Datensätze sowie deren Ladereihenfolge für die aktuell ausgewählte Seite.</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.siteSettings" resname="module.pagetsconfig_includes.siteSettings" approved="yes">
        <source>Constants from site settings</source>
        <target state="final">Konstanten aus Site-Konfiguration</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.pagetsconfig" resname="module.pagetsconfig_includes.pagetsconfig" approved="yes">
        <source>Page TSconfig</source>
        <target state="final">Page TSconfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.header.configuration" resname="module.pagetsconfig_includes.panel.header.configuration" approved="yes">
        <source>Configuration</source>
        <target state="final">Konfiguration</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.header.syntaxErrors" resname="module.pagetsconfig_includes.panel.header.syntaxErrors" approved="yes">
        <source>Syntax scanner warnings</source>
        <target state="final">Warnungen des Syntax-Scanner</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.header.conditions" resname="module.pagetsconfig_includes.panel.header.conditions" approved="yes">
        <source>Conditions</source>
        <target state="final">Bedingungen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.info.syntaxErrorCount.single" resname="module.pagetsconfig_includes.panel.info.syntaxErrorCount.single" approved="yes">
        <source>%s syntax warning</source>
        <target state="final">%s Syntax-Warnung</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.info.syntaxErrorCount.multiple" resname="module.pagetsconfig_includes.panel.info.syntaxErrorCount.multiple" approved="yes">
        <source>%s syntax warnings</source>
        <target state="final">%s Syntax-Warnungen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.info.conditionActiveCount.multiple" resname="module.pagetsconfig_includes.panel.info.conditionActiveCount.multiple" approved="yes">
        <source>%s active conditions</source>
        <target state="final">%s aktive Bedingungen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.info.conditionActiveCount.single" resname="module.pagetsconfig_includes.panel.info.conditionActiveCount.single" approved="yes">
        <source>%s active condition</source>
        <target state="final">%s aktive Bedingung</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.panel.info.conditionWithConstant" resname="module.pagetsconfig_includes.panel.info.conditionWithConstant" approved="yes">
        <source>Constant usage: [%s]</source>
        <target state="final">Konstanten-Nutzung: [%s]</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.syntaxError.sourceCode" resname="module.pagetsconfig_includes.syntaxError.sourceCode" approved="yes">
        <source>Show affected code snippet</source>
        <target state="final">Zeige betroffenes Code-Snippet</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.syntaxError.type.line.invalid" resname="module.pagetsconfig_includes.syntaxError.type.line.invalid" approved="yes">
        <source>Invalid line in "%1$s", line number "%2$s"</source>
        <target state="final">Ungültige Zeile in "%1$s", Zeilennummer "%2$s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.syntaxError.type.brace.excess" resname="module.pagetsconfig_includes.syntaxError.type.brace.excess" approved="yes">
        <source>Brace in excess in "%1$s", line number "%2$s"</source>
        <target state="final">Klammer zu viel in "%1$s", Zeilennummer "%2$s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.syntaxError.type.brace.missing" resname="module.pagetsconfig_includes.syntaxError.type.brace.missing" approved="yes">
        <source>Brace missing in "%1$s", line number "%2$s"</source>
        <target state="final">Klammer fehlt in "%1$s", Zeilennummer "%2$s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.syntaxError.type.import.empty" resname="module.pagetsconfig_includes.syntaxError.type.import.empty" approved="yes">
        <source>Import does not find a file in "%1$s", line number "%2$s"</source>
        <target state="final">Der Import findet keine Datei in "%1$s", Zeile "%2$s"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.Segment" resname="module.pagetsconfig_includes.tree.child.type.Segment" approved="yes">
        <source>Code segment</source>
        <target state="final">Code-Segment</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.AtImport" resname="module.pagetsconfig_includes.tree.child.type.AtImport" approved="yes">
        <source>Included via "@import"</source>
        <target state="final">Eingebunden via "@import"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.Condition" resname="module.pagetsconfig_includes.tree.child.type.Condition" approved="yes">
        <source>Condition (then)</source>
        <target state="final">Bedingung (then)</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.ConditionElse" resname="module.pagetsconfig_includes.tree.child.type.ConditionElse" approved="yes">
        <source>Condition (else)</source>
        <target state="final">Bedingung (else)</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.ConditionIncludeTyposcript" resname="module.pagetsconfig_includes.tree.child.type.ConditionIncludeTyposcript" approved="yes">
        <source>Included via "INCLUDE_TYPOSCRIPT" (with condition)</source>
        <target state="final">Eingebunden via "INCLUDE_TYPOSPOSCRIPT" (mit Bedingung)</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.IncludeTyposcript" resname="module.pagetsconfig_includes.tree.child.type.IncludeTyposcript" approved="yes">
        <source>Included via "INCLUDE_TYPOSCRIPT"</source>
        <target state="final">Eingebunden via "INCLUDE_TYPOSCRIPT"</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.Segment" resname="module.pagetsconfig_includes.tree.child.type.Segment" approved="yes">
        <source>Code segment</source>
        <target state="final">Code-Segment</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.type.TsConfig" resname="module.pagetsconfig_includes.tree.child.type.TsConfig" approved="yes">
        <source>TSconfig</source>
        <target state="final">TsConfig</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.conditionVerdict.matched" resname="module.pagetsconfig_includes.tree.child.conditionVerdict.matched" approved="yes">
        <source>Matched</source>
        <target state="final">Übereinstimmung</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.conditionVerdict.notMatched" resname="module.pagetsconfig_includes.tree.child.conditionVerdict.notMatched" approved="yes">
        <source>Not matched</source>
        <target state="final">Keine Übereinstimmung</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.btn.sourceCode" resname="module.pagetsconfig_includes.tree.child.btn.sourceCode" approved="yes">
        <source>Show code</source>
        <target state="final">Code anzeigen</target>
      </trans-unit>
      <trans-unit id="module.pagetsconfig_includes.tree.child.btn.sourceCodeWithResolvedIncludes" resname="module.pagetsconfig_includes.tree.child.btn.sourceCodeWithResolvedIncludes" approved="yes">
        <source>Show code including possible includes/imports</source>
        <target state="final">Code inklusive möglicher Einbindungen/Importe anzeigen</target>
      </trans-unit>
    </body>
  </file>
</xliff>
