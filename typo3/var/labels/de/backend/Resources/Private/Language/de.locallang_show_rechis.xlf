<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang_show_rechis.xlf" date="2011-10-17T20:22:34Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="title" resname="title" approved="yes">
        <source>Record History / Undo</source>
        <target state="final">Verlauf / Rückgängig</target>
      </trans-unit>
      <trans-unit id="time" resname="time" approved="yes">
        <source>Time</source>
        <target state="final">Zeit</target>
      </trans-unit>
      <trans-unit id="age" resname="age" approved="yes">
        <source>Age</source>
        <target state="final">Alter</target>
      </trans-unit>
      <trans-unit id="user" resname="user" approved="yes">
        <source>User</source>
        <target state="final">Ben<PERSON>er</target>
      </trans-unit>
      <trans-unit id="workspace" resname="workspace" approved="yes">
        <source>Workspace</source>
        <target state="final">Arbeitsumgebung</target>
      </trans-unit>
      <trans-unit id="tableUid" resname="tableUid" approved="yes">
        <source>Table:uid (Title)</source>
        <target state="final">Tabelle:Uid (Titel)</target>
      </trans-unit>
      <trans-unit id="differences" resname="differences" approved="yes">
        <source>Differences</source>
        <target state="final">Unterschiede</target>
      </trans-unit>
      <trans-unit id="rollback" resname="rollback" approved="yes">
        <source>Rollback</source>
        <target state="final">Zurücksetzen</target>
      </trans-unit>
      <trans-unit id="changes" resname="changes" approved="yes">
        <source>Changelog</source>
        <target state="final">Änderungen</target>
      </trans-unit>
      <trans-unit id="return" resname="return" approved="yes">
        <source>Return</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="returnLink" resname="returnLink" approved="yes">
        <source>Click here to go back</source>
        <target state="final">Klicken Sie hier, um zurückzukehren</target>
      </trans-unit>
      <trans-unit id="revertAll" resname="revertAll" approved="yes">
        <source>Rollback all changes shown</source>
        <target state="final">Alle gezeigten Änderungen rückgängig machen</target>
      </trans-unit>
      <trans-unit id="revertRecord" resname="revertRecord" approved="yes">
        <source>Rollback single record</source>
        <target state="final">Einzelnen Eintrag rückgängig machen</target>
      </trans-unit>
      <trans-unit id="revertField" resname="revertField" approved="yes">
        <source>Rollback single field</source>
        <target state="final">Feld wiederherstellen</target>
      </trans-unit>
      <trans-unit id="userNotFound" resname="userNotFound" approved="yes">
        <source>[Not Found]</source>
        <target state="final">[Nicht gefunden]</target>
      </trans-unit>
      <trans-unit id="externalChange" resname="externalChange" approved="yes">
        <source>Intermediate external change!</source>
        <target state="final">Externe Änderung!</target>
      </trans-unit>
      <trans-unit id="viaUser" resname="viaUser" approved="yes">
        <source>via</source>
        <target state="final">via</target>
      </trans-unit>
      <trans-unit id="sumUpChanges" resname="sumUpChanges" approved="yes">
        <source>Rollback (Preview)</source>
        <target state="final">Rückgängig machen (Vorschau)</target>
      </trans-unit>
      <trans-unit id="markState" resname="markState" approved="yes">
        <source>mark this change</source>
        <target state="final">Diese Änderung markieren</target>
      </trans-unit>
      <trans-unit id="unmarkState" resname="unmarkState" approved="yes">
        <source>unMark this change</source>
        <target state="final">Markierung der Änderung löschen</target>
      </trans-unit>
      <trans-unit id="elementHistory" resname="elementHistory" approved="yes">
        <source>You are currently seeing the history for an element</source>
        <target state="final">Sie sehen gerade den Verlauf für ein Element</target>
      </trans-unit>
      <trans-unit id="elementHistory_link" resname="elementHistory_link" approved="yes">
        <source>Show the full page history</source>
        <target state="final">Den vollständigen Seiten-Verlauf anzeigen</target>
      </trans-unit>
      <trans-unit id="linkRecordHistory" resname="linkRecordHistory" approved="yes">
        <source>Limit history to this record</source>
        <target state="final">Verlauf auf diesen Datensatz begrenzen</target>
      </trans-unit>
      <trans-unit id="differenceMsg" resname="differenceMsg" approved="yes">
        <source>In the Difference column you can see the changes at every modification of records. New changes are displayed in &lt;span class="diff-g"&gt;green colored text&lt;/span&gt; and the &lt;span class="diff-r"&gt;red colored text&lt;/span&gt; are the old values which were removed.</source>
        <target state="final">In der Spalte Unterschied sind die Änderungen bei jeder Bearbeitung des Datensatzes zu sehen. Neue Änderungen werden mit &lt;span class="diff-g"&gt;grünem Text&lt;/span&gt; dargestellt, die Änderungen mit &lt;span class="diff-r"&gt;rotem Text&lt;/span&gt; sind alte Werte, die entfernt wurden.</target>
      </trans-unit>
      <trans-unit id="fullView" resname="fullView" approved="yes">
        <source>Return to full view</source>
        <target state="final">Zurück zur Gesamtanzeige</target>
      </trans-unit>
      <trans-unit id="insert" resname="insert" approved="yes">
        <source>insert</source>
        <target state="final">einfügen</target>
      </trans-unit>
      <trans-unit id="delete" resname="delete" approved="yes">
        <source>delete</source>
        <target state="final">löschen</target>
      </trans-unit>
      <trans-unit id="mergedDifferences" resname="mergedDifferences" approved="yes">
        <source>Preview for Rollback</source>
        <target state="final">Vorschau der Wiederherstellung</target>
      </trans-unit>
      <trans-unit id="settings" resname="settings" approved="yes">
        <source>Settings</source>
        <target state="final">Einstellungen</target>
      </trans-unit>
      <trans-unit id="maxSteps" resname="maxSteps" approved="yes">
        <source>Show entries</source>
        <target state="final">Einträge anzeigen</target>
      </trans-unit>
      <trans-unit id="maxSteps_all" resname="maxSteps_all" approved="yes">
        <source>ALL</source>
        <target state="final">ALLE</target>
      </trans-unit>
      <trans-unit id="maxSteps_marked" resname="maxSteps_marked" approved="yes">
        <source>marked</source>
        <target state="final">markiert</target>
      </trans-unit>
      <trans-unit id="showDiff" resname="showDiff" approved="yes">
        <source>Show differences</source>
        <target state="final">Unterschiede anzeigen</target>
      </trans-unit>
      <trans-unit id="showDiff_no" resname="showDiff_no" approved="yes">
        <source>No</source>
        <target state="final">Nein</target>
      </trans-unit>
      <trans-unit id="showDiff_inline" resname="showDiff_inline" approved="yes">
        <source>Inline</source>
        <target state="final">Eingebettet</target>
      </trans-unit>
      <trans-unit id="showSubElements" resname="showSubElements" approved="yes">
        <source>Show sub elements</source>
        <target state="final">Unterelemente anzeigen</target>
      </trans-unit>
      <trans-unit id="no" resname="no" approved="yes">
        <source>No</source>
        <target state="final">Nein</target>
      </trans-unit>
      <trans-unit id="yes" resname="yes" approved="yes">
        <source>Yes</source>
        <target state="final">Ja</target>
      </trans-unit>
      <trans-unit id="noDifferences" resname="noDifferences" approved="yes">
        <source>There are no differences to the current page</source>
        <target state="final">Es gibt keine Unterschiede zur aktuellen Seite</target>
      </trans-unit>
      <trans-unit id="editLockIsActive" resname="editLockIsActive" approved="yes">
        <source>Editing is blocked for non-admins</source>
        <target state="final">Editieren nur für Administratoren erlaubt</target>
      </trans-unit>
    </body>
  </file>
</xliff>
