<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:backend/Resources/Private/Language/locallang_resource.xlf" date="2023-02-21T00:00:00Z" product-name="cms" target-language="de">
    <header/>
    <body>
      <trans-unit id="ajax.success" resname="ajax.success" approved="yes">
        <source>Success</source>
        <target state="final">Erfolgreich</target>
      </trans-unit>
      <trans-unit id="ajax.success.message.renamed" resname="ajax.success.message.renamed" approved="yes">
        <source>The resource "%s" has been renamed to "%s".</source>
        <target state="final">Die Ressource "%s" wurde in "%s" umbenannt.</target>
      </trans-unit>
      <trans-unit id="ajax.error" resname="ajax.error" approved="yes">
        <source>An error occurred</source>
        <target state="final">Es ist ein Fehler aufgetreten</target>
      </trans-unit>
      <trans-unit id="ajax.error.message.resourceNotFileOrFolder" resname="ajax.error.message.resourceNotFileOrFolder" approved="yes">
        <source>The resource must be a file or a folder.</source>
        <target state="final">Die Ressource muss eine Datei oder ein Ordner sein.</target>
      </trans-unit>
      <trans-unit id="ajax.error.message.resourceOutsideOfStorages" resname="ajax.error.message.resourceOutsideOfStorages" approved="yes">
        <source>You are not allowed to access files outside your storages.</source>
        <target state="final">Sie sind nicht berechtigt, auf Dateien außerhalb Ihrer zugewiesenen Freigaben zuzugreifen.</target>
      </trans-unit>
      <trans-unit id="ajax.error.message.resourceNoPermissionRename" resname="ajax.error.message.resourceNoPermissionRename" approved="yes">
        <source>You are not allowed to rename the resource.</source>
        <target state="final">Sie sind nicht berechtigt, diese Ressource umzubenennen.</target>
      </trans-unit>
      <trans-unit id="ajax.error.message.resourceNameCannotBeEmpty" resname="ajax.error.message.resourceNameCannotBeEmpty" approved="yes">
        <source>The resource name cannot be empty.</source>
        <target state="final">Der Name einer Ressource darf nicht leer sein.</target>
      </trans-unit>
    </body>
  </file>
</xliff>
