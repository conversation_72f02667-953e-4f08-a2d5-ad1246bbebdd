<?xml version="1.0" encoding="utf-8" standalone="yes"?>
<xliff version="1.0">
  <file source-language="en" datatype="plaintext" original="EXT:powermail/Resources/Private/Language/locallang_mod.xlf" date="2014-05-02T12:00:00Z" product-name="powermail" target-language="de">
    <header/>
    <body>
      <trans-unit id="mlang_tabs_tab" resname="mlang_tabs_tab" approved="yes">
        <source>Mails</source>
        <target state="final">Mail</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tabdescr" resname="mlang_labels_tabdescr" approved="yes">
        <source>List of all registered mails in database. Possibility to filter and export to CSV (Excel).</source>
        <target state="final">Powermail</target>
      </trans-unit>
      <trans-unit id="mlang_labels_tablabel" resname="mlang_labels_tablabel" approved="yes">
        <source>Powermail list</source>
        <target state="final">Powermail Liste</target>
      </trans-unit>
      <trans-unit id="pluginWizardTitle" resname="pluginWizardTitle" approved="yes">
        <source>Powermail</source>
        <target state="final">Powermail</target>
      </trans-unit>
      <trans-unit id="pluginWizardDescription" resname="pluginWizardDescription" approved="yes">
        <source>Powerful and easy mailform extension optimized for editors. Powermail offers data storing, extended input validation, advanced spam-prevention, marketing analyses and a lot of configuration settings.</source>
        <target state="final">Umfangreiche Kontaktformular Erweiterung - optimiert für Redakteure. Powermail bietet Speicherung der Daten, erweiterte Feldüberprüfung, ausführlichen Spamschutz, Marketing-Analyse und sehr viele Konfigurationsmöglichkeiten.</target>
      </trans-unit>
      <trans-unit id="powermail_pi1.title" approved="yes">
        <source>Powermail</source>
        <target state="final">Powermail</target>
      </trans-unit>
      <trans-unit id="powermail_pi1.description" approved="yes">
        <source>Powerful and easy mailform extension optimized for editors.</source>
        <target state="final">Leistungsstarke und einfache Mailform-Erweiterung optimiert für Editoren.</target>
      </trans-unit>
      <trans-unit id="powermail_pi2.title" approved="yes">
        <source>Powermail Frontend (List, Detail, Export)</source>
        <target state="final">Powermail-Frontend (Liste, Detail-Ansicht, Export)</target>
      </trans-unit>
      <trans-unit id="powermail_pi2.description" approved="yes">
        <source>Displays powermail mails in the frontend with export possibilities</source>
        <target state="final">Zeigt Powermail-Mails im Frontend mit Exportmöglichkeiten an</target>
      </trans-unit>
      <trans-unit id="powermail_pi3.title" approved="yes">
        <source>Powermail Frontend (Edit, Update, Delete)</source>
        <target state="final">Powermail-Frontend (Edit, Update, Löschen)</target>
      </trans-unit>
      <trans-unit id="powermail_pi3.description" approved="yes">
        <source>Displays an edit form and allows to delete sent emails</source>
        <target state="final">Zeigt ein Bearbeitungsformular an und erlaubt das Löschen von gesendeten E-Mails</target>
      </trans-unit>
      <trans-unit id="powermail_pi4.title" approved="yes">
        <source>Powermail Frontend (All functions)</source>
        <target state="final">Powermail-Frontend (Alle Funktionen)</target>
      </trans-unit>
      <trans-unit id="powermail_pi4.description" approved="yes">
        <source>A plugin with all actions</source>
        <target state="final">Ein Plugin mit allen Aktionen</target>
      </trans-unit>
    </body>
  </file>
</xliff>
