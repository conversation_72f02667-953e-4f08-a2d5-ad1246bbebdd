<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:info/Resources/Private/Language/locallang_webinfo.xlf" date="2011-10-17T20:22:32Z" product-name="cms" target-language="de">
    <header/>
    <body>
      <trans-unit id="page_title" resname="page_title" approved="yes">
        <source>Pagetree overview</source>
        <target state="final">Seitenbaumübersicht</target>
      </trans-unit>
      <trans-unit id="page_sysnote" resname="page_sysnote" approved="yes">
        <source>Internal notes</source>
        <target state="final">Interne Notizen</target>
      </trans-unit>
      <trans-unit id="pageInformation" resname="pageInformation" approved="yes">
        <source>Page information</source>
        <target state="final">Seiteninformationen</target>
      </trans-unit>
      <trans-unit id="pages_0" resname="pages_0" approved="yes">
        <source>Basic settings</source>
        <target state="final">Basiseinstellungen</target>
      </trans-unit>
      <trans-unit id="pages_2" resname="pages_2" approved="yes">
        <source>Cache and Age</source>
        <target state="final">Cache und Alter</target>
      </trans-unit>
      <trans-unit id="pages_1" resname="pages_1" approved="yes">
        <source>Record overview</source>
        <target state="final">Datensatzübersicht</target>
      </trans-unit>
      <trans-unit id="pages_layouts" resname="pages_layouts" approved="yes">
        <source>Layouts</source>
        <target state="final">Layouts</target>
      </trans-unit>
      <trans-unit id="actual_backend_layout" resname="actual_backend_layout" approved="yes">
        <source>Actual backend layout</source>
        <target state="final">Aktuelles Backend-Layout</target>
      </trans-unit>
      <trans-unit id="stat_type_0" resname="stat_type_0" approved="yes">
        <source>Page hits</source>
        <target state="final">Seitenzugriffe</target>
      </trans-unit>
      <trans-unit id="stat_type_1" resname="stat_type_1" approved="yes">
        <source>Total hits</source>
        <target state="final">Zugriffe insgesamt</target>
      </trans-unit>
      <trans-unit id="stat_type_2" resname="stat_type_2" approved="yes">
        <source>Hits in main sections</source>
        <target state="final">Zugriffe auf Hauptbereich</target>
      </trans-unit>
      <trans-unit id="stat_period" resname="stat_period" approved="yes">
        <source>Statistics in the period %s back to %s</source>
        <target state="final">Statistik der Zeitspanne %s zurück bis %s</target>
      </trans-unit>
      <trans-unit id="pI_crUser" resname="pI_crUser" approved="yes">
        <source>Created by</source>
        <target state="final">Erstellt von</target>
      </trans-unit>
      <trans-unit id="pI_crDate" resname="pI_crDate" approved="yes">
        <source>Created date</source>
        <target state="final">Erstellt am</target>
      </trans-unit>
      <trans-unit id="pI_lastChange" resname="pI_lastChange" approved="yes">
        <source>Last change</source>
        <target state="final">Letzte Änderung</target>
      </trans-unit>
      <trans-unit id="pI_lastChangeContent" resname="pI_lastChangeContent" approved="yes">
        <source>Last change, content</source>
        <target state="final">Letzte Änderung, Inhalt</target>
      </trans-unit>
      <trans-unit id="hits_title" resname="hits_title" approved="yes">
        <source>Hit Statistics</source>
        <target state="final">Zugriffsstatistik</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_viewPage" resname="lang_renderl10n_viewPage" approved="yes">
        <source>View page</source>
        <target state="final">Seite anzeigen</target>
      </trans-unit>
      <trans-unit id="lang_title" resname="lang_title" approved="yes">
        <source>Localization overview</source>
        <target state="final">Lokalisierungs-Übersicht</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editDefaultLanguagePage" resname="lang_renderl10n_editDefaultLanguagePage" approved="yes">
        <source>Edit default language page properties</source>
        <target state="final">Seiteneigenschaften der Standardsprache bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editLangOverlays" resname="lang_renderl10n_editLangOverlays" approved="yes">
        <source>Edit all language overlay records</source>
        <target state="final">Alle Übersetzungen bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editPageProperties" resname="lang_renderl10n_editPageProperties" approved="yes">
        <source>Edit all page properties</source>
        <target state="final">Alle Seiteneigenschaften bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editPage" resname="lang_renderl10n_editPage" approved="yes">
        <source>Edit page</source>
        <target state="final">Seite bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editPageLang" resname="lang_renderl10n_editPageLang" approved="yes">
        <source>Edit page translation</source>
        <target state="final">Übersetzung bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_badThingThereAre" resname="lang_renderl10n_badThingThereAre" approved="yes">
        <source>ERROR: there are two or more page overlay records for this language! Only one is allowed!</source>
        <target state="final">FEHLER: Es existieren zwei oder mehr Übersetzungen für diese Seite! Nur eine ist erlaubt!</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_editLanguageOverlayRecord" resname="lang_renderl10n_editLanguageOverlayRecord" approved="yes">
        <source>Edit language overlay record</source>
        <target state="final">Übersetzung bearbeiten</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_page" resname="lang_renderl10n_page" approved="yes">
        <source>Page</source>
        <target state="final">Seite</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_default" resname="lang_renderl10n_default" approved="yes">
        <source>Default</source>
        <target state="final">Standard</target>
      </trans-unit>
      <trans-unit id="lang_getlangsta_createNewTranslationHeaders" resname="lang_getlangsta_createNewTranslationHeaders" approved="yes">
        <source>Create new translation headers</source>
        <target state="final">Neue Übersetzungen erstellen</target>
      </trans-unit>
      <trans-unit id="lang_renderl10n_CEcount" resname="lang_renderl10n_CEcount" approved="yes">
        <source>Content Element Count</source>
        <target state="final">Anzahl Inhaltselemente</target>
      </trans-unit>
      <trans-unit id="moduleFunctions.depth" resname="moduleFunctions.depth" approved="yes">
        <source>Depth</source>
        <target state="final">Tiefe</target>
      </trans-unit>
      <trans-unit id="moduleFunctions.type" resname="moduleFunctions.type" approved="yes">
        <source>Type</source>
        <target state="final">Typ</target>
      </trans-unit>
      <trans-unit id="moduleFunctions.lang" resname="moduleFunctions.lang" approved="yes">
        <source>Language</source>
        <target state="final">Sprache</target>
      </trans-unit>
    </body>
  </file>
</xliff>
