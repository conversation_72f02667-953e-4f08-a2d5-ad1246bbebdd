<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/locallang_mod_file.xlf" date="2011-10-17T20:22:34Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="mlang_labels_tablabel" resname="mlang_labels_tablabel" approved="yes">
        <source>File administration on the server</source>
        <target state="final">Dateiverwaltung auf dem Server</target>
      </trans-unit>
      <trans-unit id="mlang_tabs_tab" resname="mlang_tabs_tab" approved="yes">
        <source>File</source>
        <target state="final">Datei</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.isOffline" resname="sys_file_storage.isOffline" approved="yes">
        <source>offline</source>
        <target state="final">offline</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_basePath" resname="sys_file_storage.localDriverFlexform_basePath" approved="yes">
        <source>Base path</source>
        <target state="final">Basispfad</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_basePath_placeholder" resname="sys_file_storage.localDriverFlexform_basePath_placeholder" approved="yes">
        <source>Add storage path here. For example: myPath/folder/</source>
        <target state="final">Fügen Sie hier den Speicherpfad hinzu. Zum Beispiel: meinPfad/Ordner/</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_baseUri" resname="sys_file_storage.localDriverFlexform_baseUri" approved="yes">
        <source>Base URI</source>
        <target state="final">URL der Startseite</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_baseUri_placeholder" resname="sys_file_storage.localDriverFlexform_baseUri_placeholder" approved="yes">
        <source>Use this URI to build the public url of files, instead of deriving the URI from base path</source>
        <target state="final">Verwenden Sie diese URI, um die öffentliche URL von Dateien zu erstellen, anstatt die URI vom Basispfad abzuleiten</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_pathType" resname="sys_file_storage.localDriverFlexform_pathType" approved="yes">
        <source>Path type</source>
        <target state="final">Pfadtyp</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_pathType_relative" resname="sys_file_storage.localDriverFlexform_pathType_relative" approved="yes">
        <source>relative</source>
        <target state="final">relativ</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_pathType_absolute" resname="sys_file_storage.localDriverFlexform_pathType_absolute" approved="yes">
        <source>absolute</source>
        <target state="final">absolut</target>
      </trans-unit>
      <trans-unit id="sys_file_storage.localDriverFlexform_caseSensitive" resname="sys_file_storage.localDriverFlexform_caseSensitive" approved="yes">
        <source>Uses case sensitive identifiers</source>
        <target state="final">Benutzt groß-/kleinschreibungsabhängige Bezeichner</target>
      </trans-unit>
      <trans-unit id="role_folder_temporary" resname="role_folder_temporary" approved="yes">
        <source>Temporary files</source>
        <target state="final">Temporäre Dateien</target>
      </trans-unit>
      <trans-unit id="role_folder_recycler" resname="role_folder_recycler" approved="yes">
        <source>Recycler</source>
        <target state="final">Papierkorb</target>
      </trans-unit>
    </body>
  </file>
</xliff>
