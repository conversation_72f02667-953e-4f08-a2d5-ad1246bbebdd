<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:core/Resources/Private/Language/locallang_core.xlf" date="2011-10-17T20:22:33Z" product-name="lang" target-language="de">
    <header/>
    <body>
      <trans-unit id="labels.datepicker.label" resname="labels.datepicker.label" approved="yes">
        <source>Open date picker</source>
        <target state="final">Datumsauswahl öffnen</target>
      </trans-unit>
      <trans-unit id="labels.openInNewWindow" resname="labels.openInNewWindow" approved="yes">
        <source>Open in new window</source>
        <target state="final">In neuem Fenster öffnen</target>
      </trans-unit>
      <trans-unit id="labels.goBack" resname="labels.goBack" approved="yes">
        <source>Go back</source>
        <target state="final">Zurück</target>
      </trans-unit>
      <trans-unit id="labels.makeShortcut" resname="labels.makeShortcut" approved="yes">
        <source>Create a shortcut to this page?</source>
        <target state="final">Einen Verweis auf diese Seite erzeugen?</target>
      </trans-unit>
      <trans-unit id="labels.makeBookmark" resname="labels.makeBookmark" approved="yes">
        <source>Create a bookmark to this page</source>
        <target state="final">Ein Lesezeichen für diese Seite erzeugen</target>
      </trans-unit>
      <trans-unit id="labels.alreadyBookmarked" resname="labels.alreadyBookmarked" approved="yes">
        <source>This page is already bookmarked</source>
        <target state="final">Diese Seite ist bereits mit einem Lesezeichen versehen</target>
      </trans-unit>
      <trans-unit id="labels.copyCurrentUrl" resname="labels.copyCurrentUrl" approved="yes">
        <source>Copy URL of this page</source>
        <target state="final">URL dieser Seite kopieren</target>
      </trans-unit>
      <trans-unit id="labels.lockedRecord" resname="labels.lockedRecord" approved="yes">
        <source>The user '%s' began to edit this record %s ago.</source>
        <target state="final">Der Benutzer "%s" hat mit der Bearbeitung dieses Datensatzes vor %s begonnen.</target>
      </trans-unit>
      <trans-unit id="labels.lockedRecord_content" resname="labels.lockedRecord_content" approved="yes">
        <source>The user '%s' began to edit content on this page %s ago.</source>
        <target state="final">Der Benutzer "%s" hat mit der Bearbeitung dieser Seite vor %s begonnen.</target>
      </trans-unit>
      <trans-unit id="labels.lockedRecordUser" resname="labels.lockedRecordUser" approved="yes">
        <source>The %s '%s' began to edit this record %s ago.</source>
        <target state="final">Der %s "%s" hat mit der Bearbeitung dieses Datensatzes vor %s begonnen.</target>
      </trans-unit>
      <trans-unit id="labels.lockedRecordUser_content" resname="labels.lockedRecordUser_content" approved="yes">
        <source>The %s '%s' began to edit content on this page %s ago.</source>
        <target state="final">Der %s "%s" hat mit der Bearbeitung dieser Seite vor %s begonnen.</target>
      </trans-unit>
      <trans-unit id="labels.user" resname="labels.user" approved="yes">
        <source>User</source>
        <target state="final">Benutzer</target>
      </trans-unit>
      <trans-unit id="labels.beUser" resname="labels.beUser" approved="yes">
        <source>BE-User</source>
        <target state="final">BE-Benutzer</target>
      </trans-unit>
      <trans-unit id="labels.feUser" resname="labels.feUser" approved="yes">
        <source>FE-User</source>
        <target state="final">FE-Benutzer</target>
      </trans-unit>
      <trans-unit id="labels.unknownUser" resname="labels.unknownUser" approved="yes">
        <source>-Unknown-</source>
        <target state="final">-Unbekannt-</target>
      </trans-unit>
      <trans-unit id="labels.showRecords" resname="labels.showRecords" approved="yes">
        <source>Show records</source>
        <target state="final">Datensätze anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.path" resname="labels.path" approved="yes">
        <source>Path</source>
        <target state="final">Pfad</target>
      </trans-unit>
      <trans-unit id="labels.resetFilter" resname="labels.resetFilter" approved="yes">
        <source>Reset Filter</source>
        <target state="final">Filter entfernen</target>
      </trans-unit>
      <trans-unit id="labels.table" resname="labels.table" approved="yes">
        <source>Table</source>
        <target state="final">Tabelle</target>
      </trans-unit>
      <trans-unit id="labels.upOneLevel" resname="labels.upOneLevel" approved="yes">
        <source>Up one level</source>
        <target state="final">Eine Ebene höher gehen</target>
      </trans-unit>
      <trans-unit id="labels.expandTable" resname="labels.expandTable" approved="yes">
        <source>Expand table</source>
        <target state="final">Tabelle ausklappen</target>
      </trans-unit>
      <trans-unit id="labels.collapseTable" resname="labels.collapseTable" approved="yes">
        <source>Collapse table</source>
        <target state="final">Tabelle einklappen</target>
      </trans-unit>
      <trans-unit id="labels.enterSearchString" resname="labels.enterSearchString" approved="yes">
        <source>Enter search term</source>
        <target state="final">Suchbegriff eingeben</target>
      </trans-unit>
      <trans-unit id="labels.enterSearchLevels" resname="labels.enterSearchLevels" approved="yes">
        <source>This page|1 level down|2 levels down|3 levels down|4 levels down</source>
        <target state="final">Diese Seite|Inkl. 1 Ebene|Inkl. 2 Ebenen|Inkl. 3 Ebenen|Inkl. 4 Ebenen</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.0" resname="labels.searchLevel.0" approved="yes">
        <source>This page</source>
        <target state="final">Diese Seite</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.1" resname="labels.searchLevel.1" approved="yes">
        <source>1 level down</source>
        <target state="final">1 Ebene nach unten</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.2" resname="labels.searchLevel.2" approved="yes">
        <source>2 levels down</source>
        <target state="final">2 Ebenen nach unten</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.3" resname="labels.searchLevel.3" approved="yes">
        <source>3 levels down</source>
        <target state="final">3 Ebenen nach unten</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.4" resname="labels.searchLevel.4" approved="yes">
        <source>4 levels down</source>
        <target state="final">4 Ebenen nach unten</target>
      </trans-unit>
      <trans-unit id="labels.searchLevel.infinite" resname="labels.searchLevel.infinite" approved="yes">
        <source>Infinite levels</source>
        <target state="final">Unendlich viele Ebenen</target>
      </trans-unit>
      <trans-unit id="labels.noEditPermission" resname="labels.noEditPermission" approved="yes">
        <source>Sorry, you didn't have proper permissions to perform this change.</source>
        <target state="final">Sie haben nicht die nötigen Rechte, um diese Änderung durchzuführen.</target>
      </trans-unit>
      <trans-unit id="labels.fieldsMissing" resname="labels.fieldsMissing" approved="yes">
        <source>The fields marked with an exclamation mark are not yet correctly filled in. Please complete them properly.</source>
        <target state="final">Mit einem Ausrufezeichen markierte Felder wurden noch nicht ordnungsgemäß ausgefüllt. Bitte füllen Sie sie korrekt aus.</target>
      </trans-unit>
      <trans-unit id="labels.fieldsChanged" resname="labels.fieldsChanged" xml:space="preserve" approved="yes">
				<source>There are unsaved changes in the form!
Do you want to continue WITHOUT saving?</source>
			<target state="final">Das Formular enthält nicht gespeicherte Änderungen!
Ohne Speichern fortfahren?</target></trans-unit>
      <trans-unit id="labels.remainingCharacters" resname="labels.remainingCharacters" approved="yes">
        <source>Remaining characters: {0}</source>
        <target state="final">Verbleibende Zeichen: {0}</target>
      </trans-unit>
      <trans-unit id="labels.minCharactersLeft" resname="labels.minCharactersLeft" approved="yes">
        <source>Characters missing: {0}</source>
        <target state="final">Zeichen fehlen: {0}</target>
      </trans-unit>
      <trans-unit id="labels.maxItemsAllowed" resname="labels.maxItemsAllowed" approved="yes">
        <source>A maximum of {0} child records are allowed.</source>
        <target state="final">Es sind höchstens {0} Kinddatensätze erlaubt.</target>
      </trans-unit>
      <trans-unit id="labels.noRTEfound" resname="labels.noRTEfound" approved="yes">
        <source>Notice: This field can only be edited with a Rich Text Editor. No editor is currently available or enabled.</source>
        <target state="final">Hinweis: Dieses Feld kann nur mit einem Rich-Text-Editor (RTE) bearbeitet werden. Ein solcher Editor ist jedoch momentan nicht verfügbar oder der RTE wurde deaktiviert.</target>
      </trans-unit>
      <trans-unit id="labels.recordInformation" resname="labels.recordInformation" approved="yes">
        <source>Record information</source>
        <target state="final">Datensatzinformationen</target>
      </trans-unit>
      <trans-unit id="labels.disableRTE" resname="labels.disableRTE" approved="yes">
        <source>Disable Rich Text Editor (RTE)</source>
        <target state="final">Rich-Text-Editor (RTE) deaktivieren</target>
      </trans-unit>
      <trans-unit id="labels._CONTROL_" resname="labels._CONTROL_" approved="yes">
        <source>Control</source>
        <target state="final">Bedienungselement</target>
      </trans-unit>
      <trans-unit id="labels._PATH_" resname="labels._PATH_" approved="yes">
        <source>Path</source>
        <target state="final">Pfad</target>
      </trans-unit>
      <trans-unit id="labels._LOCALIZATION_" resname="labels._LOCALIZATION_" approved="yes">
        <source>Localization</source>
        <target state="final">Lokalisierung</target>
      </trans-unit>
      <trans-unit id="labels._REF_" resname="labels._REF_" approved="yes">
        <source>Ref</source>
        <target state="final">Ref</target>
      </trans-unit>
      <trans-unit id="labels.uid" resname="labels.uid" approved="yes">
        <source>Unique ID</source>
        <target state="final">Eindeutige ID</target>
      </trans-unit>
      <trans-unit id="labels.pid" resname="labels.pid" approved="yes">
        <source>Parent page ID</source>
        <target state="final">ID der übergeordneten Seite</target>
      </trans-unit>
      <trans-unit id="labels.tstamp" resname="labels.tstamp" approved="yes">
        <source>Last modified</source>
        <target state="final">Zuletzt geändert</target>
      </trans-unit>
      <trans-unit id="labels.crdate" resname="labels.crdate" approved="yes">
        <source>Creation date</source>
        <target state="final">Erstelldatum</target>
      </trans-unit>
      <trans-unit id="labels.cruser_id" resname="labels.cruser_id" approved="yes">
        <source>Created by</source>
        <target state="final">Erstellt von</target>
      </trans-unit>
      <trans-unit id="labels.sorting" resname="labels.sorting" approved="yes">
        <source>Sorting</source>
        <target state="final">Sortierung</target>
      </trans-unit>
      <trans-unit id="labels.t3ver_state" resname="labels.t3ver_state" approved="yes">
        <source>Workspace status</source>
        <target state="final">Status der Arbeitsumgebung</target>
      </trans-unit>
      <trans-unit id="labels.t3ver_wsid" resname="labels.t3ver_wsid" approved="yes">
        <source>Workspace ID</source>
        <target state="final">ID der Arbeitsumgebung</target>
      </trans-unit>
      <trans-unit id="labels.t3ver_oid" resname="labels.t3ver_oid" approved="yes">
        <source>Live record ID</source>
        <target state="final">ID des Live-Datensatzes</target>
      </trans-unit>
      <trans-unit id="labels.setFields" resname="labels.setFields" approved="yes">
        <source>Set fields</source>
        <target state="final">Felder setzen</target>
      </trans-unit>
      <trans-unit id="labels.search" resname="labels.search" approved="yes">
        <source>Search</source>
        <target state="final">Suchen</target>
      </trans-unit>
      <trans-unit id="labels.author" resname="labels.author" approved="yes">
        <source>Author</source>
        <target state="final">Autor</target>
      </trans-unit>
      <trans-unit id="labels.note" resname="labels.note" approved="yes">
        <source>Note</source>
        <target state="final">Bemerkung</target>
      </trans-unit>
      <trans-unit id="labels.category" resname="labels.category" approved="yes">
        <source>Category</source>
        <target state="final">Kategorie</target>
      </trans-unit>
      <trans-unit id="labels.reload" resname="labels.reload" approved="yes">
        <source>Reload</source>
        <target state="final">Neu laden</target>
      </trans-unit>
      <trans-unit id="labels.language" resname="labels.language" approved="yes">
        <source>Language</source>
        <target state="final">Sprache</target>
      </trans-unit>
      <trans-unit id="labels.view" resname="labels.view" approved="yes">
        <source>View</source>
        <target state="final">Ansicht</target>
      </trans-unit>
      <trans-unit id="labels.view.layout" resname="labels.view.layout" approved="yes">
        <source>Layout</source>
        <target state="final">Layout</target>
      </trans-unit>
      <trans-unit id="labels.view.language_comparison" resname="labels.view.language_comparison" approved="yes">
        <source>Language Comparison</source>
        <target state="final">Sprachenvergleich</target>
      </trans-unit>
      <trans-unit id="labels.view.list" resname="labels.view.list" approved="yes">
        <source>List</source>
        <target state="final">Liste</target>
      </trans-unit>
      <trans-unit id="labels.view.tiles" resname="labels.view.tiles" approved="yes">
        <source>Tiles</source>
        <target state="final">Kacheln</target>
      </trans-unit>
      <trans-unit id="labels.view.showThumbnails" resname="labels.view.showThumbnails" approved="yes">
        <source>Show thumbnails</source>
        <target state="final">Vorschaubilder anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.view.showClipboard" resname="labels.view.showClipboard" approved="yes">
        <source>Show clipboard</source>
        <target state="final">Zwischenablage anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.view.showSearch" resname="labels.view.showSearch" approved="yes">
        <source>Show search</source>
        <target state="final">Suche anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.view.selectColumns" resname="labels.view.selectColumns" approved="yes">
        <source>Select columns</source>
        <target state="final">Spalten auswählen</target>
      </trans-unit>
      <trans-unit id="labels.share" resname="labels.share" approved="yes">
        <source>Share</source>
        <target state="final">Teilen</target>
      </trans-unit>
      <trans-unit id="labels.csv" resname="labels.csv" approved="yes">
        <source>Download CSV file</source>
        <target state="final">CSV-Datei herunterladen</target>
      </trans-unit>
      <trans-unit id="labels.clear_cache" resname="labels.clear_cache" approved="yes">
        <source>Clear cache for this page</source>
        <target state="final">Cache dieser Seite löschen</target>
      </trans-unit>
      <trans-unit id="labels.no_title" resname="labels.no_title" approved="yes">
        <source>No title</source>
        <target state="final">Kein Titel</target>
      </trans-unit>
      <trans-unit id="labels.cancel" resname="labels.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbruch</target>
      </trans-unit>
      <trans-unit id="labels.deactivate" resname="labels.deactivate" approved="yes">
        <source>Deactivate</source>
        <target state="final">Deaktivieren</target>
      </trans-unit>
      <trans-unit id="labels.hidden" resname="labels.hidden" approved="yes">
        <source>Hidden</source>
        <target state="final">Verborgen</target>
      </trans-unit>
      <trans-unit id="labels.starttime" resname="labels.starttime" approved="yes">
        <source>Start</source>
        <target state="final">Start</target>
      </trans-unit>
      <trans-unit id="labels.endtime" resname="labels.endtime" approved="yes">
        <source>End</source>
        <target state="final">Stopp</target>
      </trans-unit>
      <trans-unit id="labels.minutesHoursDaysYears" resname="labels.minutesHoursDaysYears" approved="yes">
        <source>min|hrs|days|yrs|min|hour|day|year</source>
        <target state="final">Min| Std| Tage| Jahre| Min| Std| Tag| Jahr</target>
      </trans-unit>
      <trans-unit id="labels.menu" resname="labels.menu" approved="yes">
        <source>Menu</source>
        <target state="final">Menü</target>
      </trans-unit>
      <trans-unit id="labels.showPage" resname="labels.showPage" approved="yes">
        <source>View webpage</source>
        <target state="final">Web-Seite anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.showList" resname="labels.showList" approved="yes">
        <source>Show record list</source>
        <target state="final">Datensatzliste anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.days" resname="labels.days" approved="yes">
        <source>days</source>
        <target state="final">Tage</target>
      </trans-unit>
      <trans-unit id="labels.depth_0" resname="labels.depth_0" approved="yes">
        <source>This page</source>
        <target state="final">Diese Seite</target>
      </trans-unit>
      <trans-unit id="labels.depth_1" resname="labels.depth_1" approved="yes">
        <source>1 level</source>
        <target state="final">1 Ebene</target>
      </trans-unit>
      <trans-unit id="labels.depth_2" resname="labels.depth_2" approved="yes">
        <source>2 levels</source>
        <target state="final">2 Ebenen</target>
      </trans-unit>
      <trans-unit id="labels.depth_3" resname="labels.depth_3" approved="yes">
        <source>3 levels</source>
        <target state="final">3 Ebenen</target>
      </trans-unit>
      <trans-unit id="labels.depth_4" resname="labels.depth_4" approved="yes">
        <source>4 levels</source>
        <target state="final">4 Ebenen</target>
      </trans-unit>
      <trans-unit id="labels.depth_infi" resname="labels.depth_infi" approved="yes">
        <source>Infinite</source>
        <target state="final">Unendlich</target>
      </trans-unit>
      <trans-unit id="labels.allow" resname="labels.allow" approved="yes">
        <source>Allow</source>
        <target state="final">Erlauben</target>
      </trans-unit>
      <trans-unit id="labels.deny" resname="labels.deny" approved="yes">
        <source>Deny</source>
        <target state="final">Verbieten</target>
      </trans-unit>
      <trans-unit id="labels.automatic" resname="labels.automatic" approved="yes">
        <source>Auto</source>
        <target state="final">Auto</target>
      </trans-unit>
      <trans-unit id="labels.enabled" resname="labels.enabled" approved="yes">
        <source>Enabled</source>
        <target state="final">Aktiviert</target>
      </trans-unit>
      <trans-unit id="labels.show" resname="labels.show" approved="yes">
        <source>Show</source>
        <target state="final">Anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.generalTab" resname="labels.generalTab" approved="yes">
        <source>General</source>
        <target state="final">Allgemein</target>
      </trans-unit>
      <trans-unit id="labels.refresh" resname="labels.refresh" approved="yes">
        <source>Reload the tree from server</source>
        <target state="final">Baumansicht aktualisieren</target>
      </trans-unit>
      <trans-unit id="labels.collapse" resname="labels.collapse" approved="yes">
        <source>Collapse all tree items</source>
        <target state="final">Alle Baumelemente einklappen</target>
      </trans-unit>
      <trans-unit id="labels.temporaryDBmount" resname="labels.temporaryDBmount" approved="yes">
        <source>Cancel temporary DB mount</source>
        <target state="final">Temporäre Datenbankfreigabe aufheben</target>
      </trans-unit>
      <trans-unit id="labels.refreshList" resname="labels.refreshList" approved="yes">
        <source>Reload list from server</source>
        <target state="final">Liste neu laden</target>
      </trans-unit>
      <trans-unit id="labels.close" resname="labels.close" approved="yes">
        <source>Close</source>
        <target state="final">Schließen</target>
      </trans-unit>
      <trans-unit id="labels.tableWizard" resname="labels.tableWizard" approved="yes">
        <source>Table Wizard</source>
        <target state="final">Tabellenassistent</target>
      </trans-unit>
      <trans-unit id="labels.new" resname="labels.new" approved="yes">
        <source>NEW</source>
        <target state="final">NEU</target>
      </trans-unit>
      <trans-unit id="labels.list" resname="labels.list" approved="yes">
        <source>List</source>
        <target state="final">Liste</target>
      </trans-unit>
      <trans-unit id="labels.link" resname="labels.link" approved="yes">
        <source>Link</source>
        <target state="final">Link</target>
      </trans-unit>
      <trans-unit id="labels.edit" resname="labels.edit" approved="yes">
        <source>Edit</source>
        <target state="final">Bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.createNew" resname="labels.createNew" approved="yes">
        <source>Create new</source>
        <target state="final">Neu anlegen</target>
      </trans-unit>
      <trans-unit id="labels.createNewPage" resname="labels.createNewPage" approved="yes">
        <source>Create new %s</source>
        <target state="final">%s neu erstellen</target>
      </trans-unit>
      <trans-unit id="labels.createNewRecord" resname="labels.createNewRecord" approved="yes">
        <source>Create new %s on page "%s"</source>
        <target state="final">%s auf Seite "%s" neu erstellen</target>
      </trans-unit>
      <trans-unit id="labels.createNewRecordRootLevel" resname="labels.createNewRecordRootLevel" approved="yes">
        <source>Create new %s on root level</source>
        <target state="final">%s auf Wurzelebene neu erstellen</target>
      </trans-unit>
      <trans-unit id="labels.editPage" resname="labels.editPage" approved="yes">
        <source>Edit %s "%s"</source>
        <target state="final">%s "%s" bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editRecord" resname="labels.editRecord" approved="yes">
        <source>Edit %s "%s" on page "%s"</source>
        <target state="final">%s "%s" auf Seite "%s" bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editRecordNoTitle" resname="labels.editRecordNoTitle" approved="yes">
        <source>Edit %s on page "%s"</source>
        <target state="final">%s auf Seite "%s" bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editRecordRootLevel" resname="labels.editRecordRootLevel" approved="yes">
        <source>Edit %s "%s" on root level</source>
        <target state="final">%s "%s" auf Wurzelebene bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editMultiplePages" resname="labels.editMultiplePages" approved="yes">
        <source>Edit multiple pages</source>
        <target state="final">Mehrere Seiten bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editMultipleRecords" resname="labels.editMultipleRecords" approved="yes">
        <source>Edit multiple "%s" records on page "%s"</source>
        <target state="final">Mehrere "%s" Datensätze auf Seite "%s " bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.editMultipleRecordsRootLevel" resname="labels.editMultipleRecordsRootLevel" approved="yes">
        <source>Edit multiple "%s" records on root level</source>
        <target state="final">Mehrere "%s" Datensätze auf Root-Ebene bearbeiten</target>
      </trans-unit>
      <trans-unit id="labels.rootLevel" resname="labels.rootLevel" approved="yes">
        <source>root level</source>
        <target state="final">Wurzelebene</target>
      </trans-unit>
      <trans-unit id="labels.addnew" resname="labels.addnew" approved="yes">
        <source>Add new</source>
        <target state="final">Neu hinzufügen</target>
      </trans-unit>
      <trans-unit id="labels.toggleall" resname="labels.toggleall" approved="yes">
        <source>Toggle all</source>
        <target state="final">Alle umschalten</target>
      </trans-unit>
      <trans-unit id="labels.toggleSelection" resname="labels.toggleSelection" approved="yes">
        <source>Toggle selection</source>
        <target state="final">Auswahl umkehren</target>
      </trans-unit>
      <trans-unit id="labels.selected" resname="labels.selected" approved="yes">
        <source>Selected Items</source>
        <target state="final">Ausgewählte Objekte</target>
      </trans-unit>
      <trans-unit id="labels.items" resname="labels.items" approved="yes">
        <source>Available Items</source>
        <target state="final">Verfügbare Objekte</target>
      </trans-unit>
      <trans-unit id="labels.remove_selected" resname="labels.remove_selected" approved="yes">
        <source>Remove selected items</source>
        <target state="final">Ausgewähltes Objekt löschen</target>
      </trans-unit>
      <trans-unit id="labels.move_to_top" resname="labels.move_to_top" approved="yes">
        <source>Move selected items to top</source>
        <target state="final">Ausgewählte Objekte zum Anfang verschieben</target>
      </trans-unit>
      <trans-unit id="labels.move_up" resname="labels.move_up" approved="yes">
        <source>Move selected items upwards</source>
        <target state="final">Ausgewählte Objekte nach oben verschieben</target>
      </trans-unit>
      <trans-unit id="labels.move_down" resname="labels.move_down" approved="yes">
        <source>Move selected items downwards</source>
        <target state="final">Ausgewählte Objekte nach unten verschieben</target>
      </trans-unit>
      <trans-unit id="labels.move_to_bottom" resname="labels.move_to_bottom" approved="yes">
        <source>Move selected items to bottom</source>
        <target state="final">Ausgewählte Objekte zum Ende verschieben</target>
      </trans-unit>
      <trans-unit id="labels.move" resname="labels.move" approved="yes">
        <source>Move this item</source>
        <target state="final">Dieses Objekt verschieben</target>
      </trans-unit>
      <trans-unit id="labels.browse_elements" resname="labels.browse_elemets" approved="yes">
        <source>Browse for elements</source>
        <target state="final">Nach Elementen suchen</target>
      </trans-unit>
      <trans-unit id="labels.browse_folder" resname="labels.browse_folder" approved="yes">
        <source>Browse for folders</source>
        <target state="final">Nach Ordnern suchen</target>
      </trans-unit>
      <trans-unit id="labels.browse_db" resname="labels.browse_db" approved="yes">
        <source>Browse for records</source>
        <target state="final">Datensätze durchblättern</target>
      </trans-unit>
      <trans-unit id="labels.clipInsert_file" resname="labels.clipInsert_file" approved="yes">
        <source>Insert %s file(s) from the clipboard</source>
        <target state="final">%s Datei(en) aus der Zwischenablage einfügen</target>
      </trans-unit>
      <trans-unit id="labels.clipInsert_db" resname="labels.clipInsert_db" approved="yes">
        <source>Insert %s record(s) from the clipboard</source>
        <target state="final">%s Datensätze aus der Zwischenablage einfügen</target>
      </trans-unit>
      <trans-unit id="labels.generalOptions" resname="labels.generalOptions" approved="yes">
        <source>General options</source>
        <target state="final">Allgemeine Optionen</target>
      </trans-unit>
      <trans-unit id="labels.generalOptions_more" resname="labels.generalOptions_more" approved="yes">
        <source>General options (continued)</source>
        <target state="final">Allgemeine Optionen (Fortsetzung)</target>
      </trans-unit>
      <trans-unit id="labels.allTables" resname="labels.allTables" approved="yes">
        <source>[All tables]</source>
        <target state="final">[Alle Tabellen]</target>
      </trans-unit>
      <trans-unit id="labels.noMatchingValue" resname="labels.noMatchingValue" approved="yes">
        <source>INVALID VALUE ("%s")</source>
        <target state="final">WERT IST NICHT ERLAUBT ("%s")</target>
      </trans-unit>
      <trans-unit id="labels.thumbmode_clip" resname="labels.thumbmode_clip" approved="yes">
        <source>Show Thumbnails on ClipBoard</source>
        <target state="final">Vorschaubilder in der Zwischenablage anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.copymode" resname="labels.copymode" approved="yes">
        <source>Copy items instead of moving them</source>
        <target state="final">Objekte kopieren statt zu verschieben</target>
      </trans-unit>
      <trans-unit id="labels.normal" resname="labels.normal" approved="yes">
        <source>Normal (single record mode)</source>
        <target state="final">Normal (Einzelauswahlmodus)</target>
      </trans-unit>
      <trans-unit id="labels.normal-description" resname="labels.normal-description" approved="yes">
        <source>"Normal" clipboard allows to copy/move one record at time.</source>
        <target state="final">Die "normale" Zwischenablage erlaubt, einen Datensatz auf einmal zu kopieren/verschieben.</target>
      </trans-unit>
      <trans-unit id="labels.cliptabs" resname="labels.cliptabs" approved="yes">
        <source>Clipboard #</source>
        <target state="final">Zwischenablage Nr.</target>
      </trans-unit>
      <trans-unit id="labels.cliptabs-name" resname="labels.cliptabs-name" approved="yes">
        <source>Clipboard #%s (multi-selection mode)</source>
        <target state="final">Zwischenablage #%s (Mehrfachauswahlmodus)</target>
      </trans-unit>
      <trans-unit id="labels.cliptabs-description" resname="labels.cliptabs-description" approved="yes">
        <source>This clipboard allows to select and edit/copy/move multiple records at once. Choose it to show a checkbox by each record.</source>
        <target state="final">Diese Zwischenablage erlaubt, mehrere Datensätze auf einmal zu kopieren/verschieben. Wählen Sie sie aus, um eine Checkbox an jedem Datensatz zu sehen.</target>
      </trans-unit>
      <trans-unit id="labels.clipboard.delete_elements" resname="labels.clipboard.delete_elements" approved="yes">
        <source>Delete elements</source>
        <target state="final">Elemente löschen</target>
      </trans-unit>
      <trans-unit id="labels.clipboard.clear_clipboard" resname="labels.clipboard.clear_clipboard" approved="yes">
        <source>Clear clipboard</source>
        <target state="final">Zwischenablage leeren</target>
      </trans-unit>
      <trans-unit id="labels.removeItem" resname="labels.removeItem" approved="yes">
        <source>Remove element</source>
        <target state="final">Element entfernen</target>
      </trans-unit>
      <trans-unit id="labels.holdDownCTRL" resname="labels.holdDownCTRL" approved="yes">
        <source>Important: Hold down the CTRL key while toggling elements in this list!</source>
        <target state="final">Achtung: Bei Änderung innerhalb dieser Liste Strg-Taste gedrückt halten!</target>
      </trans-unit>
      <trans-unit id="labels.revertSelection" resname="labels.revertSelection" approved="yes">
        <source>Revert selection</source>
        <target state="final">Auswahl rückgängig machen</target>
      </trans-unit>
      <trans-unit id="labels.setAllCheckboxes" resname="labels.setAllCheckboxes" approved="yes">
        <source>Select All Checkboxes</source>
        <target state="final">Alle Markierungsfelder auswählen</target>
      </trans-unit>
      <trans-unit id="labels.checkAll" resname="labels.checkAll" approved="yes">
        <source>Check all</source>
        <target state="final">Alle auswählen</target>
      </trans-unit>
      <trans-unit id="labels.uncheckAll" resname="labels.uncheckAll" approved="yes">
        <source>Uncheck all</source>
        <target state="final">Keine auswählen</target>
      </trans-unit>
      <trans-unit id="labels.selection" resname="labels.selection" approved="yes">
        <source>Selection:</source>
        <target state="final">Auswahl:</target>
      </trans-unit>
      <trans-unit id="labels.noActionAvailable" resname="labels.noActionAvailable" approved="yes">
        <source>No action available</source>
        <target state="final">Keine Aktion verfügbar</target>
      </trans-unit>
      <trans-unit id="labels.placeholder.override" resname="labels.placeholder.override" approved="yes">
        <source>Set element specific value (Default: "%s")</source>
        <target state="final">Elementspezifischen Wert setzen (Standard: "%s")</target>
      </trans-unit>
      <trans-unit id="labels.placeholder.override_not_available" resname="labels.placeholder.override_not_available" approved="yes">
        <source>Set element specific value (No default)</source>
        <target state="final">Elementspezifischen Wert setzen (Kein Standard vorgegeben)</target>
      </trans-unit>
      <trans-unit id="labels.nullCheckbox" resname="labels.nullCheckbox" approved="yes">
        <source>Set value</source>
        <target state="final">Wert festlegen</target>
      </trans-unit>
      <trans-unit id="labels.changeInOrig" resname="labels.changeInOrig" approved="yes">
        <source>Changed in original translation</source>
        <target state="final">Im Original geändert</target>
      </trans-unit>
      <trans-unit id="labels.expandAll" resname="labels.expandAll" approved="yes">
        <source>Expand all</source>
        <target state="final">Alle anzeigen</target>
      </trans-unit>
      <trans-unit id="labels.generatePassword" resname="labels.generatePassword" approved="yes">
        <source>Generate password</source>
        <target state="final">Passwort generieren</target>
      </trans-unit>
      <trans-unit id="labels.new_section" resname="labels.new_section" approved="yes">
        <source>Create new</source>
        <target state="final">Neu anlegen</target>
      </trans-unit>
      <trans-unit id="labels.count" resname="labels.count" approved="yes">
        <source>Quantity</source>
        <target state="final">Menge</target>
      </trans-unit>
      <trans-unit id="labels.title.searchIcon" resname="labels.title.searchIcon" approved="yes">
        <source>Toggle search toolbar</source>
        <target state="final">Such-Werkzeugleiste umschalten</target>
      </trans-unit>
      <trans-unit id="labels.title.searchString" resname="labels.title.searchString" approved="yes">
        <source>Search term</source>
        <target state="final">Suchbegriff</target>
      </trans-unit>
      <trans-unit id="labels.title.limit" resname="labels.title.limit" approved="yes">
        <source>Limit</source>
        <target state="final">Beschränkung</target>
      </trans-unit>
      <trans-unit id="labels.title.search" resname="labels.title.search" approved="yes">
        <source>Search</source>
        <target state="final">Suchen</target>
      </trans-unit>
      <trans-unit id="labels.title.search_levels" resname="labels.title.search_levels" approved="yes">
        <source>Search levels</source>
        <target state="final">Suchebenen</target>
      </trans-unit>
      <trans-unit id="labels.label.searchString" resname="labels.label.searchString" approved="yes">
        <source>Search term</source>
        <target state="final">Suchbegriff</target>
      </trans-unit>
      <trans-unit id="labels.label.limit" resname="labels.label.limit" approved="yes">
        <source>Limit</source>
        <target state="final">Beschränkung</target>
      </trans-unit>
      <trans-unit id="labels.label.search_levels" resname="labels.label.search_levels" approved="yes">
        <source>Search levels</source>
        <target state="final">Suchebenen</target>
      </trans-unit>
      <trans-unit id="labels.active" resname="labels.active" approved="yes">
        <source>Active</source>
        <target state="final">Aktiv</target>
      </trans-unit>
      <trans-unit id="labels.locked" resname="labels.locked" approved="yes">
        <source>Locked</source>
        <target state="final">Gesperrt</target>
      </trans-unit>
      <trans-unit id="labels.mfa.enabled" resname="labels.mfa.enabled" approved="yes">
        <source>MFA enabled</source>
        <target state="final">MFA aktiviert</target>
      </trans-unit>
      <trans-unit id="labels.mfa.disabled" resname="labels.mfa.disabled" approved="yes">
        <source>MFA disabled</source>
        <target state="final">MFA deaktiviert</target>
      </trans-unit>
      <trans-unit id="labels.recordReadonly" resname="labels.recordReadonly" approved="yes">
        <source>The table is defined as readonly and can't be edited.</source>
        <target state="final">Die Tabelle ist schreibgeschützt und kann nicht bearbeitet werden.</target>
      </trans-unit>
      <trans-unit id="labels.inconsistentLanguageWarning" resname="labels.inconsistentLanguageWarning" approved="yes">
        <source>No translation parent assigned</source>
        <target state="final">Der Übersetzung ist kein Elternelement zugeordnet</target>
      </trans-unit>
      <trans-unit id="labels.notEditableInWorkspace" resname="labels.notEditableInWorkspace" approved="yes">
        <source>Not editable in this workspace without "Live" edit permissions.</source>
        <target state="final">Nicht bearbeitbar in diesem Workspace ohne Bearbeitungsrechte für "Live".</target>
      </trans-unit>
      <trans-unit id="labels.editingLiveRecordsWarning" resname="labels.editingLiveRecordsWarning" approved="yes">
        <source>Caution! These records are not versioned, you will edit the live records.</source>
        <target state="final">Achtung! Diese Datensätze sind nicht versioniert. Sie bearbeiten die Live-Datensätze.</target>
      </trans-unit>
      <trans-unit id="alttext.suggestSearching" resname="alttext.suggestSearching" approved="yes">
        <source>Searching...</source>
        <target state="final">Suche läuft...</target>
      </trans-unit>
      <trans-unit id="ver.online" resname="ver.online" approved="yes">
        <source>ONLINE</source>
        <target state="final">ONLINE</target>
      </trans-unit>
      <trans-unit id="ver.mgm" resname="ver.mgm" approved="yes">
        <source>Version Management</source>
        <target state="final">Versionsverwaltung</target>
      </trans-unit>
      <trans-unit id="ver.selVer" resname="ver.selVer" approved="yes">
        <source>Select version</source>
        <target state="final">Version auswählen</target>
      </trans-unit>
      <trans-unit id="ver.swap" resname="ver.swap" approved="yes">
        <source>Swap/Publish</source>
        <target state="final">Austauschen/Veröffentlichen</target>
      </trans-unit>
      <trans-unit id="ver.swapPage" resname="ver.swapPage" approved="yes">
        <source>Publish this version of the page including content</source>
        <target state="final">Diese Version der Seite inklusive Inhalt veröffentlichen</target>
      </trans-unit>
      <trans-unit id="TYPO3_Element_Browser" resname="TYPO3_Element_Browser" approved="yes">
        <source>TYPO3 Element Browser</source>
        <target state="final">TYPO3-Element-Browser</target>
      </trans-unit>
      <trans-unit id="labels.referencesToRecord" resname="labels.referencesToRecord" approved="yes">
        <source>There are %s reference(s) to this record!</source>
        <target state="final">Auf diesen Datensatz zeigen %s Referenz(en)!</target>
      </trans-unit>
      <trans-unit id="labels.referencesToFolder" resname="labels.referencesToFolder" approved="yes">
        <source>There are %s reference(s) to this folder!</source>
        <target state="final">Auf diesen Ordner zeigen %s Referenzen!</target>
      </trans-unit>
      <trans-unit id="labels.referencesToFile" resname="labels.referencesToFile" approved="yes">
        <source>There are %s reference(s) to this file!</source>
        <target state="final">Auf diese Datei zeigen %s Referenzen!</target>
      </trans-unit>
      <trans-unit id="labels.translationsOfRecord" resname="labels.translationsOfRecord" approved="yes">
        <source>This record has %s translation(s) which will be deleted, too!</source>
        <target state="final">Für diesen Datensatz gibt es %s Übersetzung(en), die ebenfalls gelöscht werden!</target>
      </trans-unit>
      <trans-unit id="show_item.php.viewItem" resname="show_item.php.viewItem" approved="yes">
        <source>View Item</source>
        <target state="final">Element anzeigen</target>
      </trans-unit>
      <trans-unit id="show_item.php.referencesToThisItem" resname="show_item.php.referencesToThisItem" approved="yes">
        <source>References to this item</source>
        <target state="final">Referenzen auf dieses Element</target>
      </trans-unit>
      <trans-unit id="show_item.php.referencesFromThisItem" resname="show_item.php.referencesFromThisItem" approved="yes">
        <source>References from this item</source>
        <target state="final">Referenzen von diesem Element aus</target>
      </trans-unit>
      <trans-unit id="show_item.php.file" resname="show_item.php.file" approved="yes">
        <source>File</source>
        <target state="final">Datei</target>
      </trans-unit>
      <trans-unit id="show_item.php.dimensions" resname="show_item.php.dimensions" approved="yes">
        <source>Dimensions</source>
        <target state="final">Ausmaße</target>
      </trans-unit>
      <trans-unit id="show_item.php.filesize" resname="show_item.php.filesize" approved="yes">
        <source>Size</source>
        <target state="final">Dateigröße</target>
      </trans-unit>
      <trans-unit id="show_item.php.pixels" resname="show_item.php.pixels" approved="yes">
        <source>Pixels</source>
        <target state="final">Pixel</target>
      </trans-unit>
      <trans-unit id="show_item.php.files" resname="show_item.php.files" approved="yes">
        <source>files</source>
        <target state="final">Dateien</target>
      </trans-unit>
      <trans-unit id="show_item.php.cannotDisplayArchive" resname="show_item.php.cannotDisplayArchive" approved="yes">
        <source>Sorry, TYPO3_CONF_VARS[BE][disable_exec_function] was set, so cannot display content of archive file.</source>
        <target state="final">Es wurde leider TYPO3_CONF_VARS[BE][disable_exec_function] gesetzt, daher kann der Inhalt der Archivdatei nicht angezeigt werden.</target>
      </trans-unit>
      <trans-unit id="show_item.php.table" resname="show_item.php.table" approved="yes">
        <source>Table</source>
        <target state="final">Tabelle</target>
      </trans-unit>
      <trans-unit id="show_item.php.uid" resname="show_item.php.uid" approved="yes">
        <source>Uid</source>
        <target state="final">Uid</target>
      </trans-unit>
      <trans-unit id="show_item.php.title" resname="show_item.php.title" approved="yes">
        <source>Title</source>
        <target state="final">Titel</target>
      </trans-unit>
      <trans-unit id="show_item.php.field" resname="show_item.php.field" approved="yes">
        <source>Field</source>
        <target state="final">Feld</target>
      </trans-unit>
      <trans-unit id="show_item.php.flexpointer" resname="show_item.php.flexpointer" approved="yes">
        <source>Flexpointer</source>
        <target state="final">Flexzeiger</target>
      </trans-unit>
      <trans-unit id="show_item.php.softrefKey" resname="show_item.php.softrefKey" approved="yes">
        <source>Softref Key</source>
        <target state="final">Softref-Schlüssel</target>
      </trans-unit>
      <trans-unit id="show_item.php.sorting" resname="show_item.php.sorting" approved="yes">
        <source>Sorting</source>
        <target state="final">Sortierung</target>
      </trans-unit>
      <trans-unit id="show_item.php.refTable" resname="show_item.php.refTable" approved="yes">
        <source>Ref Table</source>
        <target state="final">Ref-Tabelle</target>
      </trans-unit>
      <trans-unit id="show_item.php.refUid" resname="show_item.php.refUid" approved="yes">
        <source>Ref Uid</source>
        <target state="final">Ref-Uid</target>
      </trans-unit>
      <trans-unit id="show_item.php.refString" resname="show_item.php.refString" approved="yes">
        <source>Ref String</source>
        <target state="final">Ref-String</target>
      </trans-unit>
      <trans-unit id="show_item.php.ftp_area" resname="show_item.php.ftp_area" approved="yes">
        <source>FTP AREA</source>
        <target state="final">FTP-BEREICH</target>
      </trans-unit>
      <trans-unit id="show_item.php.missing_record" resname="show_item.php.missing_record" approved="yes">
        <source>Missing</source>
        <target state="final">Fehlend</target>
      </trans-unit>
      <trans-unit id="db_new.php.pagetitle" resname="db_new.php.pagetitle" approved="yes">
        <source>New record</source>
        <target state="final">Neuer Datensatz</target>
      </trans-unit>
      <trans-unit id="db_new.php.after" resname="db_new.php.after" approved="yes">
        <source>after</source>
        <target state="final">nach</target>
      </trans-unit>
      <trans-unit id="db_new.php.inside" resname="db_new.php.inside" approved="yes">
        <source>inside</source>
        <target state="final">in</target>
      </trans-unit>
      <trans-unit id="file_upload.php.pagetitle" resname="file_upload.php.pagetitle" approved="yes">
        <source>Upload files</source>
        <target state="final">Dateien hochladen</target>
      </trans-unit>
      <trans-unit id="file_upload.php.submit" resname="file_upload.php.submit" approved="yes">
        <source>Upload files</source>
        <target state="final">Dateien hochladen</target>
      </trans-unit>
      <trans-unit id="file_upload.dropzonehint.title" resname="file_upload.dropzonehint.title" approved="yes">
        <source>Drag &amp; drop to upload files</source>
        <target state="final">Ziehen und ablegen zum Hochladen von Dateien</target>
      </trans-unit>
      <trans-unit id="file_upload.dropzonehint.message" resname="file_upload.dropzonehint.message" approved="yes">
        <source>Drop your files here, or &lt;u&gt;click, browse &amp; choose files&lt;/u&gt;</source>
        <target state="final">Legen Sie Ihre Dateien hier ab, oder &lt;u&gt;klicken Sie, durchsuchen Sie Ihren PC und wählen Sie Dateien aus&lt;/u&gt;</target>
      </trans-unit>
      <trans-unit id="file_upload.dropzone.close" resname="file_upload.dropzone.close" approved="yes">
        <source>Close file upload area</source>
        <target state="final">Dateiupload schließen</target>
      </trans-unit>
      <trans-unit id="file_upload.select-and-submit" resname="file_upload.select-and-submit" approved="yes">
        <source>Select &amp; upload files</source>
        <target state="final">Dateien auswählen und hochladen</target>
      </trans-unit>
      <trans-unit id="file_upload.maxFileSizeExceeded" resname="file_upload.maxFileSizeExceeded" approved="yes">
        <source>File &quot;{0}&quot; exceeds maximum file size of {1}!</source>
        <target state="final">Datei "{0}" überschreitet maximale Dateigröße von {1}!</target>
      </trans-unit>
      <trans-unit id="file_upload.fileNotAllowed" resname="file_upload.fileNotAllowed" approved="yes">
        <source>Filename &quot;{0}&quot; is not allowed!</source>
        <target state="final">Dateiname "{0}" ist nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="file_upload.fileExtensionExpected" resname="file_upload.fileExtensionExpected" approved="yes">
        <source>Upload failed! A file with &quot;{0}&quot; extension is expected!</source>
        <target state="final">Hochladen fehlgeschlagen! Eine Datei mit Erweiterung "{0}" wird erwartet!</target>
      </trans-unit>
      <trans-unit id="file_upload.fileExtensionDisallowed" resname="file_upload.fileExtensionDisallowed" approved="yes">
        <source>Upload failed! The file extension must not be &quot;{0}&quot;!</source>
        <target state="final">Upload fehlgeschlagen! Die Dateiendung darf nicht "{0} " sein!</target>
      </trans-unit>
      <trans-unit id="file_upload.uploadFailed" resname="file_upload.uploadFailed" approved="yes">
        <source>Upload of file &quot;{0}&quot; failed!</source>
        <target state="final">Hochladen von Datei "{0}" fehlgeschlagen!</target>
      </trans-unit>
      <trans-unit id="file_upload.upload-in-progress" resname="file_upload.upload-in-progress" approved="yes">
        <source>File upload in progress...</source>
        <target state="final">Hochladen...</target>
      </trans-unit>
      <trans-unit id="file_upload.upload-progress-info" resname="file_upload.upload-progress-info" approved="yes">
        <source>Uploading file {0} out of {1} files.</source>
        <target state="final">Hochladen von Datei {0} von {1}.</target>
      </trans-unit>
      <trans-unit id="file_upload.upload-finished" resname="file_upload.upload-finished" approved="yes">
        <source>Upload complete! Filelist reloading...</source>
        <target state="final">Hochladen fertig! Dateiliste lädt neu...</target>
      </trans-unit>
      <trans-unit id="file_upload.overwriteExistingFiles" resname="file_upload.overwriteExistingFiles" approved="yes">
        <source>Shall existing files be overwritten?</source>
        <target state="final">Bestehende Dateien überschreiben?</target>
      </trans-unit>
      <trans-unit id="file_upload.header.originalFile" resname="file_upload.header.originalFile" approved="yes">
        <source>Original file</source>
        <target state="final">Originaldatei</target>
      </trans-unit>
      <trans-unit id="file_upload.header.uploadedFile" resname="file_upload.header.uploadedFile" approved="yes">
        <source>Uploaded file</source>
        <target state="final">Hochgeladene Datei</target>
      </trans-unit>
      <trans-unit id="file_upload.header.action" resname="file_upload.header.action" approved="yes">
        <source>Action</source>
        <target state="final">Aktion</target>
      </trans-unit>
      <trans-unit id="file_upload.existingfiles.title" resname="file_upload.existingfiles.title" approved="yes">
        <source>Some files exist already</source>
        <target state="final">Einige Dateien existieren bereits</target>
      </trans-unit>
      <trans-unit id="file_upload.existingfiles.description" resname="file_upload.existingfiles.description" approved="yes">
        <source>Some files you wanted to upload exist already on the system. Please select what you want to do with each file.</source>
        <target state="final">Einige der Dateien, die Sie hochladen möchten, existieren bereits im System. Bitte wählen Sie für jede dieser Dateien die gewünschte Aktion aus.</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.skip" resname="file_upload.actions.skip" approved="yes">
        <source>Skip the file</source>
        <target state="final">Datei überspringen</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.rename" resname="file_upload.actions.rename" approved="yes">
        <source>Rename</source>
        <target state="final">Umbenennen</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.override" resname="file_upload.actions.override" approved="yes">
        <source>Overwrite</source>
        <target state="final">Überschreiben</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.use_existing" resname="file_upload.actions.use_existing" approved="yes">
        <source>Use existing file</source>
        <target state="final">Bestehende Datei verwenden</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.label" resname="file_upload.actions.all.label" approved="yes">
        <source>Alternatively, choose a mass action</source>
        <target state="final">Wählen Sie alternativ eine Massenaktion aus</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.empty" resname="file_upload.actions.all.empty" approved="yes">
        <source>Please choose</source>
        <target state="final">Bitte auswählen</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.skip" resname="file_upload.actions.all.skip" approved="yes">
        <source>Skip all files</source>
        <target state="final">Alle Dateien überspringen</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.rename" resname="file_upload.actions.all.rename" approved="yes">
        <source>Rename all files</source>
        <target state="final">Alle Dateien umbenennen</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.override" resname="file_upload.actions.all.override" approved="yes">
        <source>Overwrite all files</source>
        <target state="final">Alle Dateien überschreiben</target>
      </trans-unit>
      <trans-unit id="file_upload.actions.all.use_existing" resname="file_upload.actions.all.use_existing" approved="yes">
        <source>Use existing files</source>
        <target state="final">Bestehende Dateien verwenden</target>
      </trans-unit>
      <trans-unit id="file_upload.button.cancel" resname="file_upload.button.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbruch</target>
      </trans-unit>
      <trans-unit id="file_upload.button.continue" resname="file_upload.button.continue" approved="yes">
        <source>Continue with selected actions</source>
        <target state="final">Mit ausgewählten Aktionen fortfahren</target>
      </trans-unit>
      <trans-unit id="file_upload.reload.filelist" resname="file_upload.reload.filelist" approved="yes">
        <source>Reload filelist</source>
        <target state="final">Dateiliste neu laden</target>
      </trans-unit>
      <trans-unit id="file_upload.reload.filelist.message" resname="file_upload.reload.filelist.message" approved="yes">
        <source>To enable the controls, the filelist has to be reloaded.</source>
        <target state="final">Um die Steuerelemente zu aktivieren, muss die Dateiliste neu geladen werden.</target>
      </trans-unit>
      <trans-unit id="file_upload.reload.filelist.actions.dismiss" resname="file_upload.reload.filelist.actions.dismiss" approved="yes">
        <source>Dismiss</source>
        <target state="final">Verwerfen</target>
      </trans-unit>
      <trans-unit id="file_upload.reload.filelist.actions.reload" resname="file_upload.reload.filelist.actions.reload" approved="yes">
        <source>Reload</source>
        <target state="final">Neu laden</target>
      </trans-unit>
      <trans-unit id="file_download.prepare" resname="file_download.prepare" approved="yes">
        <source>File download is being prepared</source>
        <target state="final">Dateidownload wird vorbereitet</target>
      </trans-unit>
      <trans-unit id="file_download.invalidSelection" resname="file_download.invalidSelection" approved="yes">
        <source>The selected elements can not be downloaded</source>
        <target state="final">Die ausgewählten Elemente können nicht heruntergeladen werden</target>
      </trans-unit>
      <trans-unit id="file_download.success" resname="file_download.success" approved="yes">
        <source>File download successfully prepared</source>
        <target state="final">Dateidownload erfolgreich vorbereitet</target>
      </trans-unit>
      <trans-unit id="file_download.error" resname="file_download.error" approved="yes">
        <source>Could not prepare files for download</source>
        <target state="final">Dateien konnten nicht für den Download vorbereitet werden</target>
      </trans-unit>
      <trans-unit id="file_download.noFiles" resname="file_download.noFiles" approved="yes">
        <source>Could not find any allowed file</source>
        <target state="final">Konnte keine erlaubte Datei finden</target>
      </trans-unit>
      <trans-unit id="file_download.noFiles.message" resname="file_download.noFiles" approved="yes">
        <source>Please check your selection, since there might be file extension limitations, defined by your administrator.</source>
        <target state="final">Bitte überprüfen Sie Ihre Auswahl, da es möglicherweise Einschränkungen bei den Dateierweiterungen gibt, die von Ihrem Administrator festgelegt wurden.</target>
      </trans-unit>
      <trans-unit id="file_rename.title" resname="file_rename.title" approved="yes">
        <source>Rename</source>
        <target state="final">Umbenennen</target>
      </trans-unit>
      <trans-unit id="file_rename.label" resname="file_rename.label" approved="yes">
        <source>New filename</source>
        <target state="final">Neuer Dateiname</target>
      </trans-unit>
      <trans-unit id="file_rename.button.cancel" resname="file_rename.button.cancel" approved="yes">
        <source>Cancel</source>
        <target state="final">Abbrechen</target>
      </trans-unit>
      <trans-unit id="file_rename.button.rename" resname="file_rename.button.rename" approved="yes">
        <source>Rename</source>
        <target state="final">Umbenennen</target>
      </trans-unit>
      <trans-unit id="online_media.new_media" resname="online_media.new_media" approved="yes">
        <source>Add new media asset</source>
        <target state="final">Neue Mediendatei hinzufügen</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.label" resname="online_media.new_media.label" approved="yes">
        <source>Url of remote source</source>
        <target state="final">URL der Quelldatei</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.allowedProviders" resname="online_media.new_media.allowedProviders" approved="yes">
        <source>Allowed media providers</source>
        <target state="final">Erlaubte Medienanbieter</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.button" resname="online_media.new_media.button" approved="yes">
        <source>Add media by URL</source>
        <target state="final">Medien nach URL hinzufügen</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.submit" resname="online_media.new_media.submit" approved="yes">
        <source>Add media</source>
        <target state="final">Medien hinzufügen</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.added" resname="online_media.new_media.added" approved="yes">
        <source>Added media</source>
        <target state="final">Medien hinzugefügt</target>
      </trans-unit>
      <trans-unit id="online_media.new_media.placeholder" resname="online_media.new_media.placeholder" approved="yes">
        <source>Paste media url here...</source>
        <target state="final">Medien-URL hier einfügen...</target>
      </trans-unit>
      <trans-unit id="online_media.error.new_media.failed" resname="online_media.error.new_media.failed" approved="yes">
        <source>Adding media failed</source>
        <target state="final">Hinzufügen von Medien fehlgeschlagen</target>
      </trans-unit>
      <trans-unit id="online_media.error.invalid_url" resname="online_media.error.invalid_url" approved="yes">
        <source>Unknown/not allowed URL</source>
        <target state="final">Unbekannte/verbotene URL</target>
      </trans-unit>
      <trans-unit id="online_media.error.already_exists" resname="online_media.error.already_exists" approved="yes">
        <source>Online media "%s" does already exist in this folder.</source>
        <target state="final">Das Online-Medium "%s" existiert bereits in diesem Ordner.</target>
      </trans-unit>
      <trans-unit id="online_media.update.success" resname="online_media.update.success" approved="yes">
        <source>Successfully updated online media</source>
        <target state="final">Online-Medium erfolgreich aktualisiert</target>
      </trans-unit>
      <trans-unit id="online_media.update.error" resname="online_media.update.error" approved="yes">
        <source>Error on updating online media</source>
        <target state="final">Fehler beim Aktualisieren des Online-Mediums</target>
      </trans-unit>
      <trans-unit id="file_clipupload.php.warning_head" resname="file_clipupload.php.warning_head" approved="yes">
        <source>Upload path error</source>
        <target state="final">Pfadfehler beim Hochladen</target>
      </trans-unit>
      <trans-unit id="file_clipupload.php.warning" resname="file_clipupload.php.warning" approved="yes">
        <source>No upload-folder is found. You should create a TEMP-folder ("_temp_") in the root of one of your mounts first!</source>
        <target state="final">Kein Ordner zum Hochladen gefunden. Legen Sie zuerst einen TEMP-Ordner in einem Ihrer Hauptverzeichnisse an ("_temp_")!</target>
      </trans-unit>
      <trans-unit id="mess.redraw" resname="mess.redraw" approved="yes">
        <source>Redrawing the page will discard the current data. Do you want to continue?</source>
        <target state="final">Durch das Neuzeichnen der Seite gehen die aktuellen Daten verloren. Den Vorgang fortsetzen?</target>
      </trans-unit>
      <trans-unit id="mess.delete" resname="mess.delete" approved="yes">
        <source>Are you sure you want to delete '%s'?</source>
        <target state="final">Sind Sie sicher, dass Sie "%s" tatsächlich löschen wollen?</target>
      </trans-unit>
      <trans-unit id="mess.delete.title" resname="mess.delete" approved="yes">
        <source>Delete this record?</source>
        <target state="final">Diesen Datensatz löschen?</target>
      </trans-unit>
      <trans-unit id="mess.deleteClip" resname="mess.deleteClip" approved="yes">
        <source>Are you sure you want to delete ALL referenced elements (%s) on the clipboard?</source>
        <target state="final">Tatsächlich alle markierten Elemente (%s) aus der Zwischenablage entfernen?</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login" resname="mess.refresh_login" xml:space="preserve" approved="yes">
				<source>Your login has probably expired.
Do you want to refresh it now?</source>
			<target state="final">Ihre Anmeldung ist abgelaufen. Die Anmeldung erneuern?</target></trans-unit>
      <trans-unit id="mess.refresh_login_refresh_button" resname="mess.refresh_login_refresh_button" approved="yes">
        <source>Stay logged in!</source>
        <target state="final">Angemeldet bleiben!</target>
      </trans-unit>
      <trans-unit id="mess.refresh_direct_logout_button" resname="mess.refresh_direct_logout_button" approved="yes">
        <source>No, log out.</source>
        <target state="final">Nein, abmelden.</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_logging_in" resname="mess.refresh_login_logging_in" approved="yes">
        <source>Logging in...</source>
        <target state="final">Anmeldung läuft...</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_failed" resname="mess.refresh_login_failed" approved="yes">
        <source>Login failed</source>
        <target state="final">Fehler</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_failed_message" resname="mess.refresh_login_failed_message" approved="yes">
        <source>Password not correct.</source>
        <target state="final">Das Passwort ist falsch.</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_title" resname="mess.refresh_login_title" approved="yes">
        <source>Refresh Login to TYPO3 (User: %s)</source>
        <target state="final">Anmeldung an TYPO3 erneuern (Benutzer: %s)</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_username" resname="mess.refresh_login_username" approved="yes">
        <source>Username</source>
        <target state="final">Benutzername</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_password" resname="mess.refresh_login_password" approved="yes">
        <source>Password</source>
        <target state="final">Passwort</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_button" resname="mess.refresh_login_button" approved="yes">
        <source>Login</source>
        <target state="final">Anmelden</target>
      </trans-unit>
      <trans-unit id="mess.refresh_exit_button" resname="mess.refresh_exit_button" approved="yes">
        <source>Exit</source>
        <target state="final">Verlassen</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_emptyPassword" resname="mess.refresh_login_emptyPassword" approved="yes">
        <source>Empty password is not allowed!</source>
        <target state="final">Leeres Passwort nicht erlaubt!</target>
      </trans-unit>
      <trans-unit id="mess.please_wait" resname="mess.please_wait" approved="yes">
        <source>Please wait ...</source>
        <target state="final">Bitte warten...</target>
      </trans-unit>
      <trans-unit id="mess.be_locked" resname="mess.be_locked" approved="yes">
        <source>The TYPO3 backend is currently locked for maintenance. Leave this browser window open and the backend will automatically become available again once maintenance is complete.</source>
        <target state="final">Das TYPO3-Backend ist derzeit wegen Wartungsarbeiten gesperrt. Wenn Sie dieses Browserfenster geöffnet lassen, steht Ihnen das Backend automatisch wieder zur Verfügung, sobald die Wartungsarbeiten beendet sind.</target>
      </trans-unit>
      <trans-unit id="mess.login_about_to_expire" resname="mess.login_about_to_expire" approved="yes">
        <source>Your TYPO3 login is about to expire. Please confirm that you want to stay logged in.</source>
        <target state="final">Ihre Anmeldung an TYPO3 läuft ab. Bestätigen Sie bitte, wenn Sie weiterhin angemeldet bleiben wollen.</target>
      </trans-unit>
      <trans-unit id="mess.refresh_login_logout_button" resname="mess.refresh_login_logout_button" approved="yes">
        <source>Log me out</source>
        <target state="final">Mich abmelden</target>
      </trans-unit>
      <trans-unit id="mess.login_refresh_about_to_expire" resname="mess.login_refresh_about_to_expire" approved="yes">
        <source>Your TYPO3 login is about to expire. Please confirm that you want to be logged out.</source>
        <target state="final">Ihre TYPO3-Anmeldung läuft in Kürze ab. Bitte bestätigen Sie, dass Sie abgemeldet werden möchten.</target>
      </trans-unit>
      <trans-unit id="mess.login_about_to_expire_title" resname="mess.login_about_to_expire_title" approved="yes">
        <source>TYPO3 login expiration notice</source>
        <target state="final">Hinweis zum Ablauf der TYPO3-Anmeldung</target>
      </trans-unit>
      <trans-unit id="mess.login_expired" resname="mess.login_expired" approved="yes">
        <source>Your TYPO3 login has expired. You need to login again if you want to continue. Otherwise you can close the current browser window.</source>
        <target state="final">Ihre TYPO3-Anmeldung ist abgelaufen. Wenn Sie fortfahren möchten, ist eine erneute Anmeldung erforderlich. Andernfalls können Sie das aktuelle Browserfenster schließen.</target>
      </trans-unit>
      <trans-unit id="mess.noSelItemForEdit" resname="mess.noSelItemForEdit" approved="yes">
        <source>Please select one or more items in the list before you can edit.</source>
        <target state="final">Sie müssen mindestens ein Element auswählen, bevor Sie mit der Bearbeitung beginnen.</target>
      </trans-unit>
      <trans-unit id="mess.refreshRequired.title" resname="mess.refreshRequired.title" approved="yes">
        <source>Refresh required</source>
        <target state="final">Aktualisierung erforderlich</target>
      </trans-unit>
      <trans-unit id="mess.refreshRequired.content" resname="mess.refreshRequired.content" approved="yes">
        <source>This change will affect which fields are available in the form. Would you like to save now in order to refresh the display?</source>
        <target state="final">Diese Änderung wird die im Formular verfügbaren Felder beeinflussen. Möchten Sie jetzt speichern, um die Anzeige zu aktualisieren?</target>
      </trans-unit>
      <trans-unit id="mess.move_into" resname="mess.move_into" approved="yes">
        <source>Move "%s" into "%s"?</source>
        <target state="final">"%s" nach "%s" verschieben?</target>
      </trans-unit>
      <trans-unit id="mess.move_into_colPos" resname="mess.move_into_colPos" approved="yes">
        <source>Move "%1$s" into column "%3$s" on page "%2$s"?</source>
        <target state="final">"%1$s" in Spalte "%3$s" auf Seite "%2$s" verschieben?</target>
      </trans-unit>
      <trans-unit id="mess.move_after" resname="mess.move_after" approved="yes">
        <source>Move "%s" to after "%s"?</source>
        <target state="final">"%s" hinter "%s" verschieben?</target>
      </trans-unit>
      <trans-unit id="mess.copy_into" resname="mess.copy_into" approved="yes">
        <source>Copy "%s" into "%s"?</source>
        <target state="final">"%s" nach "%s" kopieren?</target>
      </trans-unit>
      <trans-unit id="mess.copy_into_colPos" resname="mess.copy_into_colPos" approved="yes">
        <source>Copy "%1$s" into column "%3$s" on page "%2$s"?</source>
        <target state="final">"%1$s" in Spalte "%3$s" auf Seite "%2$s" kopieren?</target>
      </trans-unit>
      <trans-unit id="mess.copy_after" resname="mess.copy_after" approved="yes">
        <source>Copy "%s" to after "%s"?</source>
        <target state="final">"%s" hinter "%s" kopieren?</target>
      </trans-unit>
      <trans-unit id="mess.copycb_into" resname="mess.copycb_into" approved="yes">
        <source>Copy all elements (%s) from the clipboard into "%s"?</source>
        <target state="final">Alle Objekte (%s) aus der Zwischenablage nach "%s" kopieren?</target>
      </trans-unit>
      <trans-unit id="mess.copycb_after" resname="mess.copycb_after" approved="yes">
        <source>Copy all elements (%s) from the clipboard to the position after "%s"?</source>
        <target state="final">Alle Objekte (%s) aus der Zwischenablage hinter "%s" kopieren?</target>
      </trans-unit>
      <trans-unit id="mess.movecb_into" resname="mess.movecb_into" approved="yes">
        <source>Move all elements (%s) from the clipboard into "%s"?</source>
        <target state="final">Alle Objekte (%s) aus der Zwischenablage nach "%s" verschieben?</target>
      </trans-unit>
      <trans-unit id="mess.movecb_after" resname="mess.movecb_after" approved="yes">
        <source>Move all elements (%s) from the clipboard to the position after "%s"?</source>
        <target state="final">Alle Objekte (%s) aus der Zwischenablage hinter "%s" verschieben?</target>
      </trans-unit>
      <trans-unit id="rm.menu" resname="rm.menu" approved="yes">
        <source>[menu]</source>
        <target state="final">[Menü]</target>
      </trans-unit>
      <trans-unit id="rm.saveDoc" resname="rm.saveDoc" approved="yes">
        <source>Save</source>
        <target state="final">Speichern</target>
      </trans-unit>
      <trans-unit id="rm.viewDoc" resname="rm.viewDoc" approved="yes">
        <source>View</source>
        <target state="final">Anzeigen</target>
      </trans-unit>
      <trans-unit id="rm.saveDocShow" resname="rm.saveDocShow" approved="yes">
        <source>Save and view page</source>
        <target state="final">Seite speichern und anzeigen</target>
      </trans-unit>
      <trans-unit id="rm.saveCloseDoc" resname="rm.saveCloseDoc" approved="yes">
        <source>Save and close</source>
        <target state="final">Speichern und schließen</target>
      </trans-unit>
      <trans-unit id="rm.saveCloseAllDocs" resname="rm.saveCloseAllDocs" approved="yes">
        <source>Save and close all</source>
        <target state="final">Alle speichern und schließen</target>
      </trans-unit>
      <trans-unit id="rm.saveNewDoc" resname="rm.saveNewDoc" approved="yes">
        <source>Save and create a new one</source>
        <target state="final">Speichern und neue erstellen</target>
      </trans-unit>
      <trans-unit id="rm.newDoc" resname="rm.newDoc" approved="yes">
        <source>New</source>
        <target state="final">Neu</target>
      </trans-unit>
      <trans-unit id="rm.duplicateDoc" resname="rm.duplicateDoc" approved="yes">
        <source>Duplicate</source>
        <target state="final">Duplizieren</target>
      </trans-unit>
      <trans-unit id="labels.recordDuplicated" resname="labels.recordDuplicated" approved="yes">
        <source>The record has been duplicated.</source>
        <target state="final">Dieser Datensatz wurde dupliziert.</target>
      </trans-unit>
      <trans-unit id="rm.closeDoc" resname="rm.closeDoc" approved="yes">
        <source>Close</source>
        <target state="final">Schließen</target>
      </trans-unit>
      <trans-unit id="rm.closeAllDocs" resname="rm.closeAllDocs" approved="yes">
        <source>Close all</source>
        <target state="final">Alle schließen</target>
      </trans-unit>
      <trans-unit id="rm.clearCache_clearCache" resname="rm.clearCache_clearCache" approved="yes">
        <source>Clear cache</source>
        <target state="final">Cache löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCache_thisPage" resname="rm.clearCache_thisPage" approved="yes">
        <source>This page</source>
        <target state="final">Diese Seite</target>
      </trans-unit>
      <trans-unit id="rm.clearCache_all" resname="rm.clearCache_all" approved="yes">
        <source>Clear FE cache</source>
        <target state="final">Alle Caches löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCache_pages" resname="rm.clearCache_pages" approved="yes">
        <source>Clear Page Cache</source>
        <target state="final">Seiten-Cache löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCache_allTypo3Conf" resname="rm.clearCache_allTypo3Conf" approved="yes">
        <source>Clear cache in typo3conf/</source>
        <target state="final">Konfigurations-Cache löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCacheMenu_all" resname="rm.clearCacheMenu_all" approved="yes">
        <source>Clear all caches</source>
        <target state="final">Alle Caches löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCacheMenu_pages" resname="rm.clearCacheMenu_pages" approved="yes">
        <source>Clear page content cache</source>
        <target state="final">Seiteninhalts-Cache löschen</target>
      </trans-unit>
      <trans-unit id="rm.clearCacheMenu_allTypo3Conf" resname="rm.clearCacheMenu_allTypo3Conf" approved="yes">
        <source>Clear configuration cache</source>
        <target state="final">Konfigurations-Cache löschen</target>
      </trans-unit>
      <trans-unit id="flushPageCachesTitle" resname="flushPageCachesTitle" approved="yes">
        <source>Flush frontend caches</source>
        <target state="final">Frontend-Caches leeren</target>
      </trans-unit>
      <trans-unit id="flushPageCachesDescription" resname="flushPageCachesDescription" approved="yes">
        <source>Clear frontend and page-related caches.</source>
        <target state="final">Frontend- und seitenbezogene Caches löschen.</target>
      </trans-unit>
      <trans-unit id="flushAllCachesTitle2" resname="flushAllCachesTitle2" approved="yes">
        <source>Flush all caches</source>
        <target state="final">Alle Caches leeren</target>
      </trans-unit>
      <trans-unit id="flushAllCachesDescription2" resname="flushAllCachesDescription2" approved="yes">
        <source>Clear all system-related caches, including localization, extension configuration, file caches and opcode caches. Rebuilding this cache may take some time.</source>
        <target state="final">Alle systembezogenen Caches löschen, inklusive des Lokalisierungs-, Erweiterungs-Konfigurations-, Datei und Opcode-Caches. Diese Caches wieder aufzubauen, kann einige Zeit in Anspruch nehmen.</target>
      </trans-unit>
      <trans-unit id="flushCaches.error" resname="flushCaches.error" approved="yes">
        <source>An error occurred</source>
        <target state="final">Es ist ein Fehler aufgetreten</target>
      </trans-unit>
      <trans-unit id="flushCaches.error.description" resname="flushCaches.error.description" approved="yes">
        <source>An error occurred while clearing the cache. It is likely not all caches were cleared as expected.</source>
        <target state="final">Beim Leeren des Caches ist ein Fehler aufgetreten. Wahrscheinlich wurden nicht alle Caches wie erwartet gelöscht.</target>
      </trans-unit>
      <trans-unit id="rm.adminFunctions" resname="rm.adminFunctions" approved="yes">
        <source>Admin functions</source>
        <target state="final">Admin-Funktionen</target>
      </trans-unit>
      <trans-unit id="rm.edit" resname="rm.edit" approved="yes">
        <source>Edit</source>
        <target state="final">Bearbeiten</target>
      </trans-unit>
      <trans-unit id="rm.export" resname="rm.export" approved="yes">
        <source>Export</source>
        <target state="final">Export</target>
      </trans-unit>
      <trans-unit id="rm.delete" resname="rm.delete" approved="yes">
        <source>Delete</source>
        <target state="final">Löschen</target>
      </trans-unit>
      <trans-unit id="message.header.fileNotDeletedHasReferences" resname="message.header.fileNotDeletedHasReferences" approved="yes">
        <source>File not deleted</source>
        <target state="final">Datei nicht gelöscht</target>
      </trans-unit>
      <trans-unit id="message.description.fileNotDeletedHasReferences" resname="message.description.fileNotDeletedHasReferences" approved="yes">
        <source>The file "%s" cannot be deleted since it is still used at the following places</source>
        <target state="final">Die Datei "%s" kann nicht gelöscht werden, da sie noch an folgenden Stellen verwendet wird</target>
      </trans-unit>
      <trans-unit id="message.header.folderNotDeletedHasFilesWithReferences" resname="message.header.folderNotDeletedHasFilesWithReferences" approved="yes">
        <source>Folder not deleted</source>
        <target state="final">Ordner nicht gelöscht</target>
      </trans-unit>
      <trans-unit id="message.description.folderNotDeletedHasFilesWithReferences" resname="message.description.folderNotDeletedHasFilesWithReferences" approved="yes">
        <source>The folder cannot be deleted since it still contains files in use.</source>
        <target state="final">Der Ordner kann nicht gelöscht werden, weil er noch benutzte Dateien enthält.</target>
      </trans-unit>
      <trans-unit id="message.header.fileHasBrokenReferences" resname="message.header.fileHasBrokenReferences" approved="yes">
        <source>File has broken references</source>
        <target state="final">Datei hat defekte Referenzen</target>
      </trans-unit>
      <trans-unit id="message.description.fileHasBrokenReferences" resname="message.description.fileHasBrokenReferences" approved="yes">
        <source>The file has %s broken reference(s) but it will be deleted regardless.</source>
        <target state="final">Die Datei %s hat mindestens eine defekte Referenz; sie wird dennoch gelöscht.</target>
      </trans-unit>
      <trans-unit id="message.header.fileNotFound" resname="message.header.fileNotFound" approved="yes">
        <source>File not found</source>
        <target state="final">Datei nicht gefunden</target>
      </trans-unit>
      <trans-unit id="message.description.fileNotFound" resname="message.description.fileNotFound" approved="yes">
        <source>The file %s is not found, possible already removed.</source>
        <target state="final">Die Datei %s wurde nicht gefunden; vielleicht wurde sie bereits entfernt.</target>
      </trans-unit>
      <trans-unit id="message.header.fileDeleted" resname="message.header.fileDeleted" approved="yes">
        <source>File deleted</source>
        <target state="final">Datei gelöscht</target>
      </trans-unit>
      <trans-unit id="message.description.fileDeleted" resname="message.description.fileDeleted" approved="yes">
        <source>The file "%s" was successfully deleted.</source>
        <target state="final">Die Datei "%s" wurde erfolgreich gelöscht.</target>
      </trans-unit>
      <trans-unit id="message.header.folderDeleted" resname="message.header.folderDeleted" approved="yes">
        <source>Folder deleted</source>
        <target state="final">Ordner gelöscht</target>
      </trans-unit>
      <trans-unit id="message.description.folderDeleted" resname="message.description.folderDeleted" approved="yes">
        <source>The folder "%s" was successfully deleted.</source>
        <target state="final">Der Ordner "%s" wurde erfolgreich gelöscht.</target>
      </trans-unit>
      <trans-unit id="message.header.folderNotDeletedHasFiles" resname="message.header.folderNotDeletedHasFiles" approved="yes">
        <source>Folder not deleted</source>
        <target state="final">Ordner nicht gelöscht</target>
      </trans-unit>
      <trans-unit id="message.description.folderNotDeletedHasFiles" resname="message.description.folderNotDeletedHasFiles" approved="yes">
        <source>The folder cannot be deleted since it still contains files.</source>
        <target state="final">Der Ordner kann nicht gelöscht werden, weil er noch Dateien enthält.</target>
      </trans-unit>
      <trans-unit id="buttons.logout" resname="buttons.logout" approved="yes">
        <source>Logout</source>
        <target state="final">Abmelden</target>
      </trans-unit>
      <trans-unit id="buttons.exit" resname="buttons.exit" approved="yes">
        <source>Exit</source>
        <target state="final">Verlassen</target>
      </trans-unit>
      <trans-unit id="buttons.exitSwitchUser" resname="buttons.exitSwitchUser" approved="yes">
        <source>Exit switch user mode</source>
        <target state="final">Benutzerwechsel-Modus verlassen</target>
      </trans-unit>
      <trans-unit id="buttons.selMenu_modules" resname="buttons.selMenu_modules" approved="yes">
        <source>MODULES</source>
        <target state="final">MODULE</target>
      </trans-unit>
      <trans-unit id="buttons.clear" resname="buttons.clear" approved="yes">
        <source>Clear</source>
        <target state="final">Löschen</target>
      </trans-unit>
      <trans-unit id="buttons.clipboard" resname="buttons.clipboard" approved="yes">
        <source>Clipboard</source>
        <target state="final">Zwischenablage</target>
      </trans-unit>
      <trans-unit id="buttons.removeAll" resname="buttons.removeAll" approved="yes">
        <source>Remove all</source>
        <target state="final">Alle entfernen</target>
      </trans-unit>
      <trans-unit id="buttons.toggleLinkExplanation" resname="buttons.toggleLinkExplanation" approved="yes">
        <source>Toggle link explanation</source>
        <target state="final">Linkerläuterung umschalten</target>
      </trans-unit>
      <trans-unit id="buttons.toggleSlugExplanation" resname="buttons.toggleSlugExplanation" approved="yes">
        <source>Toggle manual URL segment</source>
        <target state="final">Umschalten manuelles URL Segment</target>
      </trans-unit>
      <trans-unit id="buttons.recreateSlugExplanation" resname="buttons.recreateSlugExplanation" approved="yes">
        <source>Recalculate URL segment from page title</source>
        <target state="final">Neuberechnen des URL-Segments aus dem Seitentitel</target>
      </trans-unit>
      <trans-unit id="buttons.deactivateMfa" resname="buttons.deactivateMfa" approved="yes">
        <source>Deactivate multi-factor authentication</source>
        <target state="final">Multi-Faktor-Authentifizierung deaktivieren</target>
      </trans-unit>
      <trans-unit id="buttons.deactivateMfaProvider" resname="buttons.deactivateMfaProvider" approved="yes">
        <source>Deactivate %s</source>
        <target state="final">%s deaktivieren</target>
      </trans-unit>
      <trans-unit id="buttons.deactivateMfa.confirmation.text" resname="deactivateMfa.confirmation.text" approved="yes">
        <source>Are you sure you want to deactivate all active providers? This action can not be undone and will be applied immediately!</source>
        <target state="final">Sind Sie sicher, dass Sie alle aktiven Anbieter deaktivieren möchten? Diese Aktion kann nicht rückgängig gemacht werden und wird sofort angewandt!</target>
      </trans-unit>
      <trans-unit id="buttons.deactivateMfaProvider.confirmation.text" resname="deactivateMfaProvider.confirmation.text" approved="yes">
        <source>Are you sure you want to deactivate %s? This action can not be undone and will be applied immediately!</source>
        <target state="final">Sind Sie sicher, dass Sie %s deaktivieren möchten? Diese Aktion kann nicht rückgängig gemacht werden und wird sofort angewandt!</target>
      </trans-unit>
      <trans-unit id="slugCreation.success.page" resname="slugCreation.success.page" approved="yes">
        <source>This page will be reachable via %s</source>
        <target state="final">Diese Seite wird über %s erreichbar sein</target>
      </trans-unit>
      <trans-unit id="slugCreation.success.record" resname="slugCreation.success.record" approved="yes">
        <source>The URL part of this record will be %s</source>
        <target state="final">Der URL-Teil dieses Datensatzes wird %s sein</target>
      </trans-unit>
      <trans-unit id="slugCreation.error" resname="slugCreation.error" approved="yes">
        <source>The requested URL is already in use, but %s will be used instead</source>
        <target state="final">Die angeforderte URL wird bereits verwendet, aber stattdessen wird %s verwendet</target>
      </trans-unit>
      <trans-unit id="cm.copy" resname="cm.copy" approved="yes">
        <source>Copy</source>
        <target state="final">Kopieren</target>
      </trans-unit>
      <trans-unit id="cm.copyrelease" resname="cm.copyrelease" approved="yes">
        <source>Copy release</source>
        <target state="final">Aus Zwischenablage entfernen</target>
      </trans-unit>
      <trans-unit id="cm.cut" resname="cm.cut" approved="yes">
        <source>Cut</source>
        <target state="final">Ausschneiden</target>
      </trans-unit>
      <trans-unit id="cm.cutrelease" resname="cm.cutrelease" approved="yes">
        <source>Cut release</source>
        <target state="final">Fixierte Version</target>
      </trans-unit>
      <trans-unit id="cm.view" resname="cm.view" approved="yes">
        <source>Show</source>
        <target state="final">Anzeigen</target>
      </trans-unit>
      <trans-unit id="cm.edit" resname="cm.edit" approved="yes">
        <source>Edit</source>
        <target state="final">Bearbeiten</target>
      </trans-unit>
      <trans-unit id="cm.editcontent" resname="cm.editcontent" approved="yes">
        <source>Edit content</source>
        <target state="final">Inhalt bearbeiten</target>
      </trans-unit>
      <trans-unit id="cm.editMetadata" resname="cm.editMetadata" approved="yes">
        <source>Edit Metadata of this file</source>
        <target state="final">Metadaten dieser Datei bearbeiten</target>
      </trans-unit>
      <trans-unit id="cm.new" resname="cm.new" approved="yes">
        <source>New</source>
        <target state="final">Neu</target>
      </trans-unit>
      <trans-unit id="cm.newSubpage" resname="cm.newSubpage" approved="yes">
        <source>New subpage</source>
        <target state="final">Neue Unterseite</target>
      </trans-unit>
      <trans-unit id="cm.newFilemount" resname="cm.newFilemount" approved="yes">
        <source>New Filemount</source>
        <target state="final">Neue Verzeichnisfreigabe</target>
      </trans-unit>
      <trans-unit id="cm.pasteinto" resname="cm.pasteinto" approved="yes">
        <source>Paste into</source>
        <target state="final">Einfügen in</target>
      </trans-unit>
      <trans-unit id="cm.pasteafter" resname="cm.pasteafter" approved="yes">
        <source>Paste after</source>
        <target state="final">Einfügen nach</target>
      </trans-unit>
      <trans-unit id="cm.select" resname="cm.select" approved="yes">
        <source>Select</source>
        <target state="final">Auswählen</target>
      </trans-unit>
      <trans-unit id="cm.selectFile" resname="cm.selectFile" approved="yes">
        <source>Select %s</source>
        <target state="final">Wähle %s</target>
      </trans-unit>
      <trans-unit id="cm.deselect" resname="cm.deselect" approved="yes">
        <source>Deselect</source>
        <target state="final">Auswahl aufheben</target>
      </trans-unit>
      <trans-unit id="cm.selectto" resname="cm.selectto" approved="yes">
        <source>Select to</source>
        <target state="final">Auswählen bis</target>
      </trans-unit>
      <trans-unit id="cm.deselectto" resname="cm.deselectto" approved="yes">
        <source>Deselect to</source>
        <target state="final">Ausw. aufheben bis</target>
      </trans-unit>
      <trans-unit id="cm.delete" resname="cm.delete" approved="yes">
        <source>Delete</source>
        <target state="final">Löschen</target>
      </trans-unit>
      <trans-unit id="cm.hide" resname="cm.hide" approved="yes">
        <source>Hide</source>
        <target state="final">Verbergen</target>
      </trans-unit>
      <trans-unit id="cm.unhide" resname="cm.unhide" approved="yes">
        <source>Unhide</source>
        <target state="final">Sichtbar machen</target>
      </trans-unit>
      <trans-unit id="cm.online" resname="cm.online" approved="yes">
        <source>Turn online</source>
        <target state="final">Online schalten</target>
      </trans-unit>
      <trans-unit id="cm.offline" resname="cm.offline" approved="yes">
        <source>Turn offline</source>
        <target state="final">Offline schalten</target>
      </trans-unit>
      <trans-unit id="cm.upload" resname="cm.upload" approved="yes">
        <source>Upload Files</source>
        <target state="final">Dateien hochladen</target>
      </trans-unit>
      <trans-unit id="cm.rename" resname="cm.rename" approved="yes">
        <source>Rename</source>
        <target state="final">Umbenennen</target>
      </trans-unit>
      <trans-unit id="cm.replace" resname="cm.replace" approved="yes">
        <source>Replace</source>
        <target state="final">Ersetzen</target>
      </trans-unit>
      <trans-unit id="cm.open" resname="cm.open" approved="yes">
        <source>Open</source>
        <target state="final">Öffnen</target>
      </trans-unit>
      <trans-unit id="cm.save" resname="cm.save" approved="yes">
        <source>Save</source>
        <target state="final">Speichern</target>
      </trans-unit>
      <trans-unit id="cm.unzip" resname="cm.unzip" approved="yes">
        <source>Unzip</source>
        <target state="final">Entpacken</target>
      </trans-unit>
      <trans-unit id="cm.info" resname="cm.info" approved="yes">
        <source>Info</source>
        <target state="final">Info</target>
      </trans-unit>
      <trans-unit id="cm.createnew" resname="cm.createnew" approved="yes">
        <source>Create new</source>
        <target state="final">Neu anlegen</target>
      </trans-unit>
      <trans-unit id="cm.createnew.link" resname="cm.createnew.link" approved="yes">
        <source>Create new %s</source>
        <target state="final">%s neu erstellen</target>
      </trans-unit>
      <trans-unit id="cm.tempMountPoint" resname="cm.tempMountPoint" approved="yes">
        <source>Mount as treeroot</source>
        <target state="final">Als Startpunkt für den Seitenbaum festlegen</target>
      </trans-unit>
      <trans-unit id="cm.copyPage_into" resname="cm.copyPage_into" approved="yes">
        <source>Copy page into</source>
        <target state="final">Seite kopieren in</target>
      </trans-unit>
      <trans-unit id="cm.copyPage_after" resname="cm.copyPage_after" approved="yes">
        <source>Copy page after</source>
        <target state="final">Seite kopieren hinter</target>
      </trans-unit>
      <trans-unit id="cm.movePage_into" resname="cm.movePage_into" approved="yes">
        <source>Move page into</source>
        <target state="final">Seite verschieben in</target>
      </trans-unit>
      <trans-unit id="cm.movePage_after" resname="cm.movePage_after" approved="yes">
        <source>Move page after</source>
        <target state="final">Seite verschieben hinter</target>
      </trans-unit>
      <trans-unit id="cm.copyFolder_into" resname="cm.copyFolder_into" approved="yes">
        <source>Copy folder into</source>
        <target state="final">Ordner kopieren in</target>
      </trans-unit>
      <trans-unit id="cm.moveFolder_into" resname="cm.moveFolder_into" approved="yes">
        <source>Move folder into</source>
        <target state="final">Ordner verschieben in</target>
      </trans-unit>
      <trans-unit id="cm.createNewRelation" resname="cm.createNewRelation" approved="yes">
        <source>Create new relation</source>
        <target state="final">Neue Relation erstellen</target>
      </trans-unit>
      <trans-unit id="cm.branchActions" resname="cm.branchActions" approved="yes">
        <source>Branch Actions</source>
        <target state="final">Teilbereichsaktionen</target>
      </trans-unit>
      <trans-unit id="cm.copyPasteActions" resname="cm.copyPasteActions" approved="yes">
        <source>Page Actions</source>
        <target state="final">Seitenaktionen</target>
      </trans-unit>
      <trans-unit id="cm.collapseBranch" resname="cm.collapseBranch" approved="yes">
        <source>Collapse Branch</source>
        <target state="final">Teilbereich einklappen</target>
      </trans-unit>
      <trans-unit id="cm.expandBranch" resname="cm.expandBranch" approved="yes">
        <source>Expand Branch</source>
        <target state="final">Teilbereich ausklappen</target>
      </trans-unit>
      <trans-unit id="cm.more" resname="cm.more" approved="yes">
        <source>More options...</source>
        <target state="final">Weitere Optionen...</target>
      </trans-unit>
      <trans-unit id="cm.allowedFileExtensions" resname="cm.allowedFileExtensions" approved="yes">
        <source>Allowed file extensions</source>
        <target state="final">Erlaubte Dateierweiterungen</target>
      </trans-unit>
      <trans-unit id="cm.disallowedFileExtensions" resname="cm.disallowedFileExtensions" approved="yes">
        <source>Disallowed file extensions</source>
        <target state="final">Unzulässige Dateiendungen</target>
      </trans-unit>
      <trans-unit id="cm.allowedEditableTextFileExtensions" resname="cm.allowedEditableTextFileExtensions" approved="yes">
        <source>Allowed editable text file extensions</source>
        <target state="final">Zugelassene bearbeitbare Erweiterungen für Text-Dateien</target>
      </trans-unit>
      <trans-unit id="cm.allowEmbedSources" resname="cm.allowEmbedSources" approved="yes">
        <source>Allowed media sources</source>
        <target state="final">Erlaubte Medienanbieter</target>
      </trans-unit>
      <trans-unit id="cm.allowedRelations" resname="cm.allowedRelations" approved="yes">
        <source>Allowed relations</source>
        <target state="final">Zugelassene Relationen</target>
      </trans-unit>
      <trans-unit id="cm.history" resname="CM_history" approved="yes">
        <source>History/Undo</source>
        <target state="final">Verlauf/Rückgängig</target>
      </trans-unit>
      <trans-unit id="cm.transferToClipboard" resname="cm.transferToClipboard" approved="yes">
        <source>Transfer to clipboard</source>
        <target state="final">In die Zwischenablage übertragen</target>
      </trans-unit>
      <trans-unit id="cm.removeFromClipboard" resname="cm.removeFromClipboard" approved="yes">
        <source>Remove from clipboard</source>
        <target state="final">Aus der Zwischenablage entfernen</target>
      </trans-unit>
      <trans-unit id="sortable.dragmove" resname="sortable.dragmove" approved="yes">
        <source>Drag to move</source>
        <target state="final">Ziehen zum Bewegen</target>
      </trans-unit>
      <trans-unit id="tree.defaultPageTitle" resname="tree.defaultPageTitle" approved="yes">
        <source>[Default Title]</source>
        <target state="final">[Standard-Titel]</target>
      </trans-unit>
      <trans-unit id="tree.activeFilterMode" resname="tree.activeFilterMode" approved="yes">
        <source>Active Filtering Mode</source>
        <target state="final">Filtermodus aktiv</target>
      </trans-unit>
      <trans-unit id="tree.buttonNewNode" resname="tree.buttonNewNode" approved="yes">
        <source>Create new pages</source>
        <target state="final">Neue Seiten erstellen</target>
      </trans-unit>
      <trans-unit id="tree.buttonFilter" resname="tree.buttonFilter" approved="yes">
        <source>Filter tree</source>
        <target state="final">Baum filtern</target>
      </trans-unit>
      <trans-unit id="tree.searchTermInfo" resname="tree.searchTermInfo" approved="yes">
        <source>Enter search term</source>
        <target state="final">Suchbegriff eingeben</target>
      </trans-unit>
      <trans-unit id="warning.backend_admin" resname="warning.backend_admin" approved="yes">
        <source>A backend user "admin" with password "password" is still present. %sEdit this account%s, either deleting it completely or changing the username and password.</source>
        <target state="final">Der Backend-Benutzer "admin" mit dem Passwort "password" ist noch vorhanden. %sÄndern Sie dieses Konto%s durch Änderung des Benutzernamens und Passworts oder löschen Sie den Backend-Benutzer Datensatz vollständig.</target>
      </trans-unit>
      <trans-unit id="warning.file_deny_pattern" resname="warning.file_deny_pattern" approved="yes">
        <source>The value of fileDenyPattern is not set to its default:%s If TYPO3 is running on Apache, a customized value might enable backend or frontend users to execute malicious php scripts.</source>
        <target state="final">Für fileDenyPattern ist nicht der Standardwert definiert: %s Wird TYPO3 mit Apache betrieben, kann ein angepasster Wert dazu führen, dass Backend- oder Frontend-Benutzer in die Lage versetzt werden, bösartige PHP-Skripte auszuführen.</target>
      </trans-unit>
      <trans-unit id="warning.file_deny_pattern_partsNotPresent" resname="warning.file_deny_pattern_partsNotPresent" approved="yes">
        <source>Security Risk! The new value of fileDenyPattern misses parts of its default:%s If TYPO3 is running on Apache, a customized value might enable backend or frontend users to execute malicious php scripts.</source>
        <target state="final">Sicherheitsrisiko! Der neue Wert von fileDenyPattern enthält nicht alle Teile des Standardwertes %s. Wenn TYPO3 auf einem Apache-Server ausgeführt wird, kann ein veränderter Wert Backend- oder Frontend-Benutzer in die Lage versetzen, bösartige PHP-Skripte auszuführen.</target>
      </trans-unit>
      <trans-unit id="warning.file_deny_htaccess" resname="warning.file_deny_htaccess" approved="yes">
        <source>The current value of fileDenyPattern allows to upload/create files with the name ".htaccess". If TYPO3 is running on Apache, this enables backend or frontend users to create and execute php scripts. Please reset the value of fileDenyPattern to its default.</source>
        <target state="final">Der aktuelle Wert für fileDenyPattern erlaubt das Hochladen/Anlegen von Dateien mit dem Namen ".htaccess". Wird TYPO3 mit Apache betrieben, werden Backend- und Frontend-Benutzer in die Lage versetzt, PHP-Skripte zu erstellen und auszuführen. Setzen Sie bitte fileDenyPattern auf die Voreinstellung zurück.</target>
      </trans-unit>
      <trans-unit id="warning.install_enabled" resname="warning.install_enabled" approved="yes">
        <source>The Install Tool is permanently enabled. Delete the file "%s" when you have finished setting up TYPO3.</source>
        <target state="final">Das Installationsprogramm ist dauerhaft aktiviert. Löschen Sie die Datei "%s", wenn Sie mit der Installation von TYPO3 fertig sind.</target>
      </trans-unit>
      <trans-unit id="warning.install_enabled_cmd" resname="warning.install_enabled_cmd" approved="yes">
        <source>Click to remove the file now!</source>
        <target state="final">Klicken Sie hier, um die Datei jetzt zu löschen!</target>
      </trans-unit>
      <trans-unit id="warning.install_trustedhosts" resname="warning.install_trustedhosts" approved="yes">
        <source>The trusted hosts pattern check is disabled. Please define the allowed hosts in the [SYS][trustedHostsPattern] section of the Install Tool.</source>
        <target state="final">Die Überprüfung des Musters für vertrauenswürdige Rechner ist deaktiviert. Bitte legen Sie die erlaubten Rechner im Abschnitt [SYS][trustedHostsPattern] des Install-Tools fest.</target>
      </trans-unit>
      <trans-unit id="warning.install_update" resname="warning.install_update" approved="yes">
        <source>This installation is not configured for the TYPO3 version it is running. If you did so intentionally, this message can be safely ignored. If you are unsure, visit the Upgrade Wizard section of the %sInstall Tool%s to see how TYPO3 would change.</source>
        <target state="final">Diese Installation ist nicht für die aktuell eingesetzte TYPO3-Version konfiguriert. Haben Sie dies mit Absicht getan, können Sie die Nachricht beruhigt ignorieren. Wenn Sie unsicher sind, rufen Sie das "Upgrade-Wizard" im Modul "Admin Tools&gt;Upgrade" oder über im %sTYPO3 Install Tool%s auf, um mögliche notwendige Änderungen zu überprüfen bzw. auszuführen.</target>
      </trans-unit>
      <trans-unit id="warning.header" resname="warning.header" approved="yes">
        <source>Important Notice!</source>
        <target state="final">Wichtiger Hinweis!</target>
      </trans-unit>
      <trans-unit id="warning.backend_reference" resname="warning.backend_reference" approved="yes">
        <source>The Reference Index table is empty which is likely the result of a recent TYPO3 upgrade. Please go to %sTools&gt;DB Check%s and update the reference index.</source>
        <target state="final">Die Referenzindextabelle ist leer. Dies ist wahrscheinlich die Folge einer kürzlich erfolgten TYPO3-Aktualisierung. Bitte den Punkt %sWerkzeuge &gt; DB-Prüfung%s aufrufen und den Referenzindex aktualisieren.</target>
      </trans-unit>
      <trans-unit id="warning.backend_reference_index" resname="warning.backend_reference_index" approved="yes">
        <source>The Reference Index table is empty which is likely the result of a recent TYPO3 upgrade. The Reference Index was last updated on %3$s. Please go to %sSystem&gt;DB Check%s and update the reference index.</source>
        <target state="final">Die Referenz-Index Tabelle ist leer. Dies ist wahrscheinlich die Folge eines kürzlich durchgeführten TYPO3 Upgrades. Der Referenz-Index wurde zuletzt am %3$s aktualisiert. Bitte rufen Sie das Modul %sSystem&gt;DB-Überprüfung%s auf und aktualisieren Sie den Referenz-Index.</target>
      </trans-unit>
      <trans-unit id="warning.memcache_not_usable" resname="warning.memcache_not_usable" approved="yes">
        <source>Memcache is configured, but connection to memcached failed with these configuration(s)</source>
        <target state="final">Memcache wurde konfiguriert, aber Verbindung zu Memcache ist fehlgeschlagen mit folgender/n Konfiguration(en)</target>
      </trans-unit>
      <trans-unit id="warning.file_missing" resname="warning.file_missing" approved="yes">
        <source>File is missing!</source>
        <target state="final">Datei fehlt!</target>
      </trans-unit>
      <trans-unit id="warning.file_missing_text" resname="warning.file_missing_text" approved="yes">
        <source>This file is marked as missing</source>
        <target state="final">Dieses Datei ist als fehlend markiert</target>
      </trans-unit>
      <trans-unit id="warning.header.storage_is_no_public" resname="warning.header.storage_is_no_public" approved="yes">
        <source>Storage is not public</source>
        <target state="final">Speicher ist nicht öffentlich</target>
      </trans-unit>
      <trans-unit id="warning.message.storage_is_no_public" resname="warning.message.storage_is_no_public" approved="yes">
        <source>The storage has been marked to be "publicly available" but is not detected as such by the driver. The setting has been reverted.</source>
        <target state="final">Der Speicher wurde als "öffentlich verfügbar" markiert, wurde aber nicht als solcher vom Treiber erkannt. Die Einstellung wurde zurückgesetzt.</target>
      </trans-unit>
      <trans-unit id="warning.inline_use_combination" resname="warning.inline_use_combination" approved="yes">
        <source>This record will be modified globally.</source>
        <target state="final">Dieser Datensatz wird global verändert.</target>
      </trans-unit>
      <trans-unit id="warning.tableDisplayOrder.message" resname="warning.tableDisplayOrder.message" approved="yes">
        <source>The TSconfig setting for "mod.web_list.tableDisplayOrder" contains a circular dependency and cannot be resolved.</source>
        <target state="final">Die TSconfig Einstellung für "mod.web_list.tableDisplayOrder" enthält einen Zirkelbezug und kann nicht aufgelöst werden.</target>
      </trans-unit>
      <trans-unit id="warning.tableDisplayOrder.title" resname="warning.tableDisplayOrder.title" approved="yes">
        <source>Circular table ordering detected</source>
        <target state="final">Zirkelbezug in der Sortierungsreihenfolge erkannt</target>
      </trans-unit>
      <trans-unit id="error.formProtection.tokenInvalid" resname="error.formProtection.tokenInvalid" approved="yes">
        <source>Validating the security token of this form has failed. Please reload the form and submit it again.</source>
        <target state="final">Die Validierung des Sicherheitstokens dieses Formulars ist fehlgeschlagen. Bitte laden Sie das Formular erneut und schicken Sie es dann noch einmal ab.</target>
      </trans-unit>
      <trans-unit id="toolbarItems.shortcuts" resname="toolbarItems.shortcuts" approved="yes">
        <source>Shortcuts</source>
        <target state="final">Verweise</target>
      </trans-unit>
      <trans-unit id="toolbarItems.shortcut" resname="toolbarItems.shortcut" approved="yes">
        <source>Shortcut</source>
        <target state="final">Verweis</target>
      </trans-unit>
      <trans-unit id="toolbarItems.shortcutsGroup" resname="toolbarItems.shortcutsGroup" approved="yes">
        <source>Shortcut Group</source>
        <target state="final">Verweisgruppe</target>
      </trans-unit>
      <trans-unit id="toolbarItems.shortcutsEdit" resname="toolbarItems.shortcutsEdit" approved="yes">
        <source>Edit Shortcut</source>
        <target state="final">Verweis bearbeiten</target>
      </trans-unit>
      <trans-unit id="toolbarItems.shortcutsDelete" resname="toolbarItems.shortcutsDelete" approved="yes">
        <source>Delete Shortcut</source>
        <target state="final">Verweis löschen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarks" resname="toolbarItems.bookmarks" approved="yes">
        <source>Bookmarks</source>
        <target state="final">Lesezeichen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmark" resname="toolbarItems.bookmark" approved="yes">
        <source>Bookmark</source>
        <target state="final">Lesezeichen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarksGroup" resname="toolbarItems.bookmarksGroup" approved="yes">
        <source>Bookmark group</source>
        <target state="final">Lesezeichengruppe</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarksEdit" resname="toolbarItems.bookmarksEdit" approved="yes">
        <source>Edit bookmark</source>
        <target state="final">Lesezeichen bearbeiten</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarksDelete" resname="toolbarItems.bookmarksDelete" approved="yes">
        <source>Delete bookmark</source>
        <target state="final">Lesezeichen löschen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.collapsePageTree" resname="toolbarItems.collapsePageTree" approved="yes">
        <source>Collapse page tree</source>
        <target state="final">Seitenbaum einklappen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.confirmBookmarksDelete" resname="toolbarItems.confirmBookmarksDelete" approved="yes">
        <source>Do you really want to remove this bookmark?</source>
        <target state="final">Möchten Sie dieses Lesezeichen tatsächlich entfernen?</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkSavedTitle" resname="toolbarItems.bookmarkSavedTitle" approved="yes">
        <source>Bookmark saved</source>
        <target state="final">Lesezeichen gespeichert</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkSavedMessage" resname="toolbarItems.bookmarkSavedMessage" approved="yes">
        <source>The bookmark was saved.</source>
        <target state="final">Das Lesezeichen wurde gespeichert.</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkAlreadyExistsTitle" resname="toolbarItems.bookmarkAlreadyExistsTitle" approved="yes">
        <source>Bookmark already exists</source>
        <target state="final">Lesezeichen existiert bereits</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkAlreadyExistsMessage" resname="toolbarItems.bookmarkAlreadyExistsMessage" approved="yes">
        <source>The document is already in your bookmarks.</source>
        <target state="final">Das Dokument ist bereits in Ihren Lesezeichen.</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkFailedTitle" resname="toolbarItems.bookmarkFailedTitle" approved="yes">
        <source>Bookmark creation failed</source>
        <target state="final">Erstellung des Lesezeichen fehlgeschlagen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.bookmarkFailedMessage" resname="toolbarItems.bookmarkFailedMessage" approved="yes">
        <source>The bookmark couldn't get created</source>
        <target state="final">Das Lesezeichen konnte nicht erstellt werden</target>
      </trans-unit>
      <trans-unit id="toolbarItems.help" resname="toolbarItems.help" approved="yes">
        <source>Help</source>
        <target state="final">Hilfe</target>
      </trans-unit>
      <trans-unit id="toolbarItems.minMaxModuleMenu" resname="toolbarItems.minMaxModuleMenu" approved="yes">
        <source>Minimize/maximize module menu</source>
        <target state="final">Modulmenü minimieren/maximieren</target>
      </trans-unit>
      <trans-unit id="toolbarItems.search" resname="toolbarItems.search" approved="yes">
        <source>Search</source>
        <target state="final">Suchen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.toolbarOpenClose" resname="toolbarItems.toolbarOpenClose" approved="yes">
        <source>Open/Close toolbar</source>
        <target state="final">Symbolleiste öffnen/schließen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.searchbarOpenClose" resname="toolbarItems.searchbarOpenClose" approved="yes">
        <source>Open/Close searchbar</source>
        <target state="final">Suchleiste öffnen/schließen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.workspace" resname="toolbarItems.workspace" approved="yes">
        <source>Workspace</source>
        <target state="final">Arbeitsumgebung</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo" resname="toolbarItems.sysinfo" approved="yes">
        <source>System Information</source>
        <target state="final">Systeminformationen</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.phpversion" resname="toolbarItems.sysinfo.phpversion" approved="yes">
        <source>PHP Version</source>
        <target state="final">PHP-Version</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.debugger" resname="toolbarItems.sysinfo.debugger" approved="yes">
        <source>Debugger</source>
        <target state="final">Debugger</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.database" resname="toolbarItems.sysinfo.database" approved="yes">
        <source>Database</source>
        <target state="final">Datenbank</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.database.offline" resname="toolbarItems.sysinfo.database.offline" approved="yes">
        <source>Offline</source>
        <target state="final">Offline</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.applicationcontext" resname="toolbarItems.sysinfo.applicationcontext" approved="yes">
        <source>Application Context</source>
        <target state="final">Anwendungskontext</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.composerMode" resname="toolbarItems.sysinfo.composerMode" approved="yes">
        <source>Composer mode</source>
        <target state="final">Composer-Modus</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.gitrevision" resname="toolbarItems.sysinfo.gitrevision" approved="yes">
        <source>GIT Revision</source>
        <target state="final">GIT-Revision</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.operatingsystem" resname="toolbarItems.sysinfo.operatingsystem" approved="yes">
        <source>Operating System</source>
        <target state="final">Betriebssystem</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.webserver" resname="toolbarItems.sysinfo.webserver" approved="yes">
        <source>Webserver</source>
        <target state="final">Webserver</target>
      </trans-unit>
      <trans-unit id="toolbarItems.sysinfo.typo3-version" resname="toolbarItems.sysinfo.typo3-version" approved="yes">
        <source>TYPO3 Version</source>
        <target state="final">TYPO3-Version</target>
      </trans-unit>
      <trans-unit id="extension.not.installed" resname="extension.not.installed" approved="yes">
        <source>Extension "%s" is not installed.</source>
        <target state="final">Erweiterung "%s" ist nicht installiert.</target>
      </trans-unit>
      <trans-unit id="link.to.filefile.correctly" resname="link.to.filefile.correctly" approved="yes">
        <source>Please, do not link to this page.</source>
        <target state="final">Bitte verlinken Sie nicht auf diese Seite.</target>
      </trans-unit>
      <trans-unit id="error.database_schema_mismatch" resname="error.database_schema_mismatch" approved="yes">
        <source>A SQL error occurred. This may indicate a schema mismatch between TCA and the database. Try running database compare in the Install Tool.</source>
        <target state="final">Es ist ein SQL-Fehler aufgetreten. Dies kann auf eine Schema-Abweichung zwischen TCA und der Datenbank hindeuten. Versuchen Sie im Install Tool einen Datenbank-Vergleich durchzuführen.</target>
      </trans-unit>
      <trans-unit id="error.database_schema_mismatch_title" resname="error.database_schema_mismatch_title" approved="yes">
        <source>Database Error</source>
        <target state="final">Datenbank-Fehler</target>
      </trans-unit>
      <trans-unit id="error.items_proc_func_error" resname="error.items_proc_func_error" approved="yes">
        <source>An error occurred trying to process items for field "%1$s" (%2$s).</source>
        <target state="final">Bei dem Versuch, die Elemente für Feld "%1$s" (%2$s) zu verarbeiten, ist ein Fehler aufgetreten.</target>
      </trans-unit>
      <trans-unit id="error.invalidEmail" resname="error.invalidEmail" approved="yes">
        <source>"%s" is not a valid e-mail address.</source>
        <target state="final">"%s" ist keine gültige E-Mail-Adresse.</target>
      </trans-unit>
      <trans-unit id="error.adminCanNotChangeSystemMaintainer" resname="error.adminCanNotChangeSystemMaintainer" approved="yes">
        <source>Only system maintainers can change the admin flag and password of other system maintainers. The value has not been updated.</source>
        <target state="final">Nur Systemverantwortliche können das Administrator-Flag und die Passworte von anderen Systemverantwortlichen ändern. Der Wert wurde nicht aktualisiert.</target>
      </trans-unit>
      <trans-unit id="formEngine.beUser.admin.information.userIsSystemMaintainer" resname="formEngine.beUser.admin.information.userIsSystemMaintainer" approved="yes">
        <source>This user is a system maintainer</source>
        <target state="final">Der Benutzer ist ein Systemverantwortlicher</target>
      </trans-unit>
      <trans-unit id="formEngine.beUser.admin.information.userWillBecomeSystemMaintainer" resname="formEngine.beUser.admin.information.userWillBecomeSystemMaintainer" approved="yes">
        <source>This user is in the list of allowed system maintainers and will gain system level access if enabling admin access.</source>
        <target state="final">Der Benutzer ist in der Liste der zugelassenen Systemverantwortlichen und wird Vollzugriff auf das System erhalten, wenn das Administrator-Zugriff aktiviert wird.</target>
      </trans-unit>
      <trans-unit id="formEngine.beUser.admin.information.userAdminAndPasswordChangeNotAllowed" resname="formEngine.beUser.admin.information.userAdminAndPasswordChangeNotAllowed" approved="yes">
        <source>This user is a system maintainer. Changing the admin flag and changing password is denied.</source>
        <target state="final">Dieser Benutzer ist ein Systemverantwortlicher. Ändern des Administrator-Flags und des Passwortes ist nicht möglich.</target>
      </trans-unit>
      <trans-unit id="formEngine.pages.backendLayout.information.inheritFromParentPage" resname="formEngine.pages.backendLayout.information.inheritedFromParentPage" approved="yes">
        <source>This page will most likely use backend layout "%s", inherited from a parent page.</source>
        <target state="final">Diese Seite wird sehr wahrscheinlich das Backend-Layout "%s" verwenden, das von einer übergeordneten Seite geerbt wurde.</target>
      </trans-unit>
      <trans-unit id="formEngine.pages.backendLayout.information.inheritedFromParentPage" resname="formEngine.pages.backendLayout.information.inheritFromParentPage" approved="yes">
        <source>This page is currently using backend layout "%s", inherited from a parent page.</source>
        <target state="final">Diese Seite verwendet derzeit das Backend-Layout "%s", das von einer übergeordneten Seite geerbt wurde.</target>
      </trans-unit>
      <trans-unit id="error.backendUserGroupListTypeError.header" resname="error.backendUserGroupListTypeError.header" approved="yes">
        <source>Possible misconfiguration detected</source>
        <target state="final">Mögliche Fehlkonfiguration festgestellt</target>
      </trans-unit>
      <trans-unit id="error.backendUserGroupListTypeError.message" resname="error.backendUserGroupListTypeError.message" approved="yes">
        <source>Editing of at least one plugin was enabled but editing the page content type "Insert Plugin" is still disallowed. Group members won't be able to edit plugins unless you activate editing for the content type.</source>
        <target state="final">Die Bearbeitung zumindest eines Plug-Ins wurde aktiviert, aber die Bearbeitung des Seiteninhaltstyps "Plug-In einfügen" ist noch deaktiviert. Gruppenmitglieder werden keine Plug-Ins bearbeiten können, wenn Sie die Bearbeitung für den Inhaltstyp nicht erlauben.</target>
      </trans-unit>
      <trans-unit id="error.missingLanguageIsocode" resname="error.missingLanguageIsocode" approved="yes">
        <source>Missing ISO code for language "%s" (UID %d), please edit the language record and set it.</source>
        <target state="final">Fehlender ISO-Code für Sprache "%s" (UID %d). Bitte bearbeiten Sie den Sprachdatensatz und setzen Sie den ISO-Code.</target>
      </trans-unit>
      <trans-unit id="error.misconfiguredPasswordRules" resname="error.misconfiguredPasswordRules" approved="yes">
        <source>No password could be generated due to misconfigured password rules.</source>
        <target state="final">Aufgrund falsch konfigurierter Passwortregeln konnte kein Passwort generiert werden.</target>
      </trans-unit>
      <trans-unit id="labels.deleteTitle" resname="labels.deleteTitle" approved="yes">
        <source>Revert to default Constant</source>
        <target state="final">Konstante auf Standardeinstellung zurücksetzen</target>
      </trans-unit>
      <trans-unit id="labels.editTitle" resname="labels.editTitle" approved="yes">
        <source>Edit this Constant</source>
        <target state="final">Diese Konstante bearbeiten</target>
      </trans-unit>
      <trans-unit id="pageTranslation" resname="pageTranslation" approved="yes">
        <source>Page Translation</source>
        <target state="final">Seitenübersetzung</target>
      </trans-unit>
      <trans-unit id="filestorage.invalidpathexception.title" resname="filestorage.invalidpathexception.title" approved="yes">
        <source>Error in Path</source>
        <target state="final">Fehler im Pfad</target>
      </trans-unit>
      <trans-unit id="filestorage.invalidpathexception.message" resname="filestorage.invalidpathexception.message" approved="yes">
        <source>You entered an invalid path for this File Storage. Note that "../" and "//" are not allowed when using an absolute path.</source>
        <target state="final">Der Pfad für diesen Speicher ist ungültig. Beachten Sie, dass bei absoluten Pfaden "../" und "//" nicht erlaubt sind.</target>
      </trans-unit>
      <trans-unit id="iframe.listFrame" resname="iframe.listFrame" approved="yes">
        <source>List frame (content area) to create and modify pages, content and records</source>
        <target state="final">Liste (Inhaltsbereich) zum Erstellen und Bearbeiten von Seiten, Inhalten und Datensätzen</target>
      </trans-unit>
      <trans-unit id="message.confirmation" resname="message.confirmation" approved="yes">
        <source>Are you sure?</source>
        <target state="final">Sind Sie sicher?</target>
      </trans-unit>
      <trans-unit id="labels.datepicker.today" resname="labels.datepicker.today" approved="yes">
        <source>Today</source>
        <target state="final">Heute</target>
      </trans-unit>
      <trans-unit id="labels.inputfield.clearButton.title" resname="labels.inputfield.clearButton.title" approved="yes">
        <source>Clear input</source>
        <target state="final">Eingabe löschen</target>
      </trans-unit>
      <trans-unit id="labels.contextMenu.open" resname="labels.contextMenu.open" approved="yes">
        <source>Open context menu</source>
        <target state="final">Kontextmenü öffnen</target>
      </trans-unit>
      <trans-unit id="labels.contextMenu.open.recordLabel" resname="labels.contextMenu.open.recordLabel" approved="yes">
        <source>Open context menu for record: %s</source>
        <target state="final">Kontextmenü für Datensatz öffnen: %s</target>
      </trans-unit>
    </body>
  </file>
</xliff>
