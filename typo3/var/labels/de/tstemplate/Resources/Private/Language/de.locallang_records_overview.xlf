<?xml version="1.0" encoding="UTF-8"?>
<xliff xmlns="urn:oasis:names:tc:xliff:document:1.2" version="1.2">
  <file source-language="en" datatype="plaintext" original="EXT:tstemplate/Resources/Private/Language/locallang_records_overview.xlf" date="2023-01-22T19:53:00Z" product-name="tstemplate" target-language="de">
    <header/>
    <body>
      <trans-unit id="typoscriptRecords.title" resname="typoscriptRecords.title" approved="yes">
        <source>TypoScript records overview</source>
        <target state="final">TypoScript Datensatzübersicht</target>
      </trans-unit>
      <trans-unit id="typoscriptRecords.description" resname="typoscriptRecords.description" approved="yes">
        <source>Global overview of all pages in the database containing one or more TypoScript records.</source>
        <target state="final">Globale Übersicht aller Seiten in der Datenbank mit einem oder mehreren TypoScript Datensätzen.</target>
      </trans-unit>
      <trans-unit id="typoscriptRecords.noRecordsFound" resname="typoscriptRecords.noRecordsFound" approved="yes">
        <source>No TypoScript Records found.</source>
        <target state="final">Keine TypoScript-Datensätze gefunden.</target>
      </trans-unit>
      <trans-unit id="typoscriptRecords.table.column.pageTitle" resname="typoscriptRecords.table.column.pageTitle" approved="yes">
        <source>Page title</source>
        <target state="final">Seitentitel</target>
      </trans-unit>
      <trans-unit id="typoscriptRecords.table.column.typoscriptRecords" resname="typoscriptRecords.table.column.typoscriptRecords" approved="yes">
        <source>TypoScript records</source>
        <target state="final">TypoScript-Datensätze</target>
      </trans-unit>
      <trans-unit id="typoscriptRecords.table.column.root" resname="typoscriptRecords.table.column.root" approved="yes">
        <source>Marked as root</source>
        <target state="final">Als Root markiert</target>
      </trans-unit>
    </body>
  </file>
</xliff>
