<?php
/**
 * We have the following contexts:
 * - Production
 * - Production/Staging
 * - Development/Local
 */
$env = (string) TYPO3\CMS\Core\Core\Environment::getContext();

if ($env === 'Production' || $env === 'Production/Testing') {
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['host']     = getenv('MYSQL_HOST');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['dbname']   = getenv('MYSQL_DATABASE');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['user']     = getenv('MYSQL_USER');
    $GLOBALS['TYPO3_CONF_VARS']['DB']['Connections']['Default']['password'] = getenv('MYSQL_PASSWORD');

    // Register exception handler
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['productionExceptionHandler']
        = Networkteam\SentryClient\ProductionExceptionHandler::class;
    // Forward log messages to Sentry
    $GLOBALS['TYPO3_CONF_VARS']['LOG']['writerConfiguration'] = [
        \TYPO3\CMS\Core\Log\LogLevel::ERROR => [
            \Networkteam\SentryClient\SentryLogWriter::class => [],
        ],
    ];

} else if ($env === 'Development/Local') {
    $GLOBALS['TYPO3_CONF_VARS']['BE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['FE']['debug'] = true;
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['displayErrors'] = '1';
    $GLOBALS['TYPO3_CONF_VARS']['SYS']['devIPmask'] = '*';

}

//MinIO file storage
if ($env === 'Production') {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_1'] = [
        'key'           => getenv('MINIO_USER'),
        'secretKey'     => getenv('MINIO_PASSWORD'),
        'publicBaseUrl' => getenv('MINIO_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];

} else {
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_1'] = [
        'key'           => getenv('MINIO_PROD_USER'),
        'secretKey'     => getenv('MINIO_PROD_PASSWORD'),
        'customHost'    => getenv('MINIO_PROD_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_PROD_PUBLIC_URL'),
        'protocol'      => 'https://',
    ];
    $GLOBALS['TYPO3_CONF_VARS']['EXTENSIONS']['aus_driver_amazon_s3']['storage_2'] = [
        'key'           => getenv('MINIO_LOCAL_USER'),
        'secretKey'     => getenv('MINIO_LOCAL_PASSWORD'),
        'customHost'    => getenv('MINIO_LOCAL_CUSTOM_HOST'),
        'publicBaseUrl' => getenv('MINIO_LOCAL_PUBLIC_URL'),
        'protocol'      => 'auto',
    ];
}

if ($env === 'Development/Local') {
    $extraConfFile = __DIR__ . '/additional.development.php';
    if (file_exists($extraConfFile)) {
        include $extraConfFile;
    }
}

