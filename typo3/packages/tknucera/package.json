{"private": true, "scripts": {"build": "NODE_ENV=dev yarn encore dev", "build-prod": "NODE_ENV=production yarn encore production"}, "devDependencies": {"@symfony/webpack-encore": "^1.3.0", "alpinejs": "^3.3.5", "autoprefixer": "^10.4.12", "core-js": "^3.16.1", "postcss": "^8.4.18", "postcss-loader": "^5.0.0", "prettier": "^2.3.2", "prettier-plugin-tailwind": "^2.2.12", "sass": "^1.34.1", "sass-loader": "^11.0.0", "svg-spritemap-webpack-plugin": "^4.4.0", "tailwindcss": "^3.2.0", "webpack-notifier": "^1.6.0"}, "dependencies": {"@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/line-clamp": "^0.4.2", "@tailwindcss/typography": "^0.5.7"}}