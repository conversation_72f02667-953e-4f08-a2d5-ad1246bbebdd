#Hide all other new content wizard tabs
mod.wizards.newContentElement.wizardItems {
    common.show := removeFromList(bullets, header, image, table, text, textmedia, textpic, uploads)
    #forms.show := removeFromList(felogin_login)
    srh >
}

# language label/icon
mod.SHARED {
    defaultLanguageFlag = de
    defaultLanguageLabel = Deutsch
}

TCEFORM.tt_content {
    #remove content element types from dropdown
    CType.removeItems (
        bullets, header, image, table, text, textmedia, textpic, uploads,
        #felogin_login,
        menu_abstract, menu_categorized_content, menu_categorized_pages,
        menu_pages, menu_recently_updated, menu_related_pages,
        menu_section_pages, menu_sitemap_pages, menu_sitemap, menu_subpages,
        div,
    )

    ## Hide content element fields
    #General: Headlines
    header_layout.disabled = 1
    header_position.disabled = 1
    date.disabled = 1
    header_link.disabled = 1
    subheader.disabled = 1

    #Layout
    layout.disabled = 1
    frame_class.disabled = 1
    space_before_class.disabled = 1
    space_after_class.disabled = 1

    #Layout: Links
    linkToTop.disabled = 1

    header.label = Bezeichnung im Backend
    header.disabled = 0
    header.types.list.disabled = 0
    # Keep header layout for plugins
    header_layout.disabled = 1
    header_layout.types.list.disabled = 0
    # only keep "hidden"
    header_layout.removeItems = 0,1,2,3,4,5
}

# default values
TCAdefaults.tt_content {
    # Title layout: hidden/verborgen (used in plugins)
    header_layout = 100

    #no wrapper div around content elements
    frame_class = none
}
