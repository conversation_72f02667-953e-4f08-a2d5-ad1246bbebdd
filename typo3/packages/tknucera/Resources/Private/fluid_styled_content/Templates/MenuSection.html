<html xmlns:f="http://typo3.org/ns/TYPO3/CMS/Fluid/ViewHelpers" data-namespace-typo3-fluid="true">
<f:layout name="Default" />
<f:section name="Main">

    <f:if condition="{menu.0.content}">
      <f:variable name="menuCollapse" value="0"/>
      <f:variable name="bgColor" value="bg-grey-50"/>
      <f:if condition="{data.tx_lde_toc_collapse}">
        <f:variable name="menuCollapse" value="{data.tx_lde_toc_collapse}"/>
      </f:if>
      <f:if condition="{data.tx_lde_toc_background}">
        <f:variable name="bgColor" value="{data.tx_lde_toc_background}"/>
      </f:if>
        <div x-data="<f:format.raw>{</f:format.raw>menusAreOpen: {menuCollapse}<f:format.raw>}</f:format.raw>" class="group p-4 md:p-6 lg:p-10 text-blue-800 rounded-lg {bgColor} {data.tx_lde_toc_class}">
            <div @click="menusAreOpen = !menusAreOpen" class="flex items-center gap-4 text-2xl font-medium cursor-pointer">
                <span>Inhalt</span>
                <svg x-cloak x-show="!menusAreOpen" class="inline-block" height="16">
                    <use x-show="menusAreOpen" href="/typo3conf/ext/lde/Resources/Public/Assets/spritemap_general.svg#sprite-plus"></use>
                </svg>
                <svg x-cloak x-show="menusAreOpen" class="inline-block" height="16">
                    <use href="/typo3conf/ext/lde/Resources/Public/Assets/spritemap_general.svg#sprite-minus"></use>
                </svg>
            </div>
            <ol x-cloak class="flex flex-col gap-2 pt-6" x-show="menusAreOpen">
                <f:for each="{menu}" as="page">
                    <f:if condition="{page.content}">
                        <f:for each="{page.content}" as="element">
                            <f:if condition="{element.data.header}">
                            <li class="text-base font-medium">
                                <a class="inline-flex gap-4" href="{page.link}#c{element.data.uid}"{f:if(condition: page.target, then: ' target="{page.target}"')} title="{element.data.header}">
                                    <div class="w-[12px] h-[12px]">
                                      <svg class="inline-block -rotate-90" height="12" width="12">
                                        <use href="/typo3conf/ext/lde/Resources/Public/Assets/spritemap_general.svg#sprite-chevron-down"></use>
                                      </svg>
                                    </div>
                                    <span>{element.data.header}</span>
                                </a>
                            </li>
                            </f:if>
                        </f:for>
                    </f:if>
                </f:for>
            </ol>
        </div>
    </f:if>

</f:section>
</html>
