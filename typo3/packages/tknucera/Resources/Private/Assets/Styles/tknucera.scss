@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    @font-face {
        font-family: 'CorporateFont';
        font-style: normal;
        font-weight: normal;
        font-display: swap;
        src: url('../Fonts/transitoffc-webfont.woff2') format('woff2'),
        url('../Fonts/transitoffc-webfont.woff') format('woff');
    }
    @font-face {
        font-family: 'CorporateFont-Bold';
        font-style: normal;
        font-weight: normal;
        font-display: swap;
        src: url('../Fonts/transitoffc-bold-webfont.woff2') format('woff2'),
        url('../Fonts/transitoffc-bold-webfont.woff') format('woff');
    }
    @font-face {
        font-family: 'CorporateFont-Black';
        font-style: normal;
        font-weight: normal;
        font-display: swap;
        src: url('../Fonts/transitoffc-black-webfont.woff2') format('woff2'),
        url('../Fonts/transitoffc-black-webfont.woff') format('woff');
    }
    @font-face {
        font-family: 'CorporateFont-Italic';
        font-style: italic;
        font-weight: normal;
        font-display: swap;
        src: url('../Fonts/transitoffc-italic-webfont.woff2') format('woff2'),
        url('../Fonts/transitoffc-italic-webfont.woff') format('woff');
    }
}

@layer components {
    body {
        @apply font-corporate text-black;
    }
    .corporate_grid {
        @apply grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12 w-full max-w-[1280px] mx-auto;
    }

    .corporate_grid_full {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_halfLeft {
        @apply col-start-1 md:col-start-1 lg:col-start-1 col-end-5 md:col-end-5 lg:col-end-7;
    }

    .corporate_grid_halfRight {
        @apply col-start-1 md:col-start-5 lg:col-start-7 col-end-5 md:col-end-9 lg:col-end-13;
    }

    .corporate_grid_flex3Cols {
        @apply flex flex-col md:flex-row gap-4 md:gap-6 lg:gap-8;
    }

    .corporate_grid_flex3Cols .corporate_grid_flexCol {
        @apply w-full md:w-1/3;
    }

    /* headlines */
    .h1 {
        @apply font-corporateBlack text-h1-mobile lg:text-h1 leading-h1-mobile lg:leading-h1;
    }

    .h2 {
        @apply font-corporateBold text-h2-mobile lg:text-h2 leading-h2-mobile lg:leading-h2;
    }

    .h3 {
        @apply font-corporateBold text-h3-mobile lg:text-h3 leading-h3-mobile lg:leading-h3;
    }

    .subtitle {
        @apply text-subtitle-mobile lg:text-subtitle leading-subtitle-mobile lg:leading-subtitle;
    }

    p a {
        @apply underline;
    }

    .h1:has(+ .h3) {
        /* if spacing should be different for headline sets */
    }

    p {
        @apply text-normal-mobile md:text-normal leading-normal-mobile md:leading-normal;
    }

    .small {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .small p {
        @apply text-small-mobile md:text-small leading-small-mobile md:leading-small;
    }

    .extrasmall p {
        @apply text-extrasmall-mobile md:text-extrasmall leading-extrasmall-mobile md:leading-extrasmall;
    }

    .big p {
        @apply text-big-mobile md:text-big leading-big-mobile md:leading-big;
    }

    .button {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-corporateBold text-white bg-primaryBlue rounded-lg px-6 py-4 hover:shadow-lg group-hover:shadow-lg outline-primaryBlue outline-[1px] focus:outline-primaryBlue-140 focus:outline-[2px] group-focus:outline-primaryBlue-140 group-focus:outline-[2px] active:outline-primaryBlue-140 active:bg-primaryBlue-140 group-active:outline-primaryBlue-140 group-active:bg-primaryBlue-140 disabled:bg-primaryBlue-60 disabled:pointer-events-none;
    }

    .button-medium {
        @apply button text-small leading-small py-3;
    }

    .button-small {
        @apply button text-small leading-small py-2 px-4;
    }

    .button-rounded {
        @apply button rounded-full p-0 w-[52px] h-[52px] justify-center
    }

    .button-medium-rounded {
        @apply button-medium rounded-full p-0 w-[44px] h-[44px] justify-center
    }

    .button-small-rounded {
        @apply button-small rounded-full p-0 w-[36px] h-[36px] justify-center
    }

    .button-white {
        @apply inline-flex flex-row items-center gap-3 flex-nowrap text-normal leading-normal font-corporateBold text-primaryBlue bg-white rounded-lg px-6 py-3.5 hover:shadow-lg outline-primaryBlue border border-solid border-primaryBlue focus:outline-primaryBlue-140 focus:outline-[2px] disabled:border-primaryBlue-60 disabled:text-primaryBlue-60 disabled:pointer-events-none;
    }

    .button-medium-white {
        @apply button-white text-small leading-small py-2.5;
    }

    .button-small-white {
        @apply button-white text-small leading-small py-1.5 px-4;
    }

    .button-white-rounded {
        @apply button-white rounded-full p-0 w-[50px] h-[50px] justify-center;
    }

    .button-medium-white-rounded {
        @apply button-medium-white rounded-full p-0 w-[42px] h-[42px] justify-center;
    }

    .button-small-white-rounded {
        @apply button-small-white rounded-full p-0 w-[34px] h-[34px] justify-center;
    }

    /* only for styleguide */
    .layoutPlaceholder {
        @apply bg-[#666666] w-full text-white p-4;
    }

    .colorList {
        @apply flex flex-row gap-6 mb-12 flex-wrap;
    }
    .colorList > div {
        @apply h-24 w-24 rounded-full shadow-lg;
    }

    .highlightHeadline span {
        @apply inline-block px-2 -mr-4 lg:px-3.5 lg:-mr-7 lg:py-0.5 mb-4;
    }

    h2.highlightHeadline span {
        @apply px-1.5 -mr-3 lg:px-2.5 lg:-mr-5 mb-4;
    }

    .highlightPositive span {
        @apply bg-primaryBlue text-white;
    }

    .highlightNegative span {
        @apply bg-white text-primaryBlue;
    }

}

@layer utilities {
    .font-smaller {
        font-size: 75%;
    }
    .font-larger {
        font-size: 120%;
    }
}

.forced-screen-width {
    width: 100vw;
    position: relative;
    left: 50%;
    right: 50%;
    margin-left: -50vw;
    margin-right: -50vw;
}

.forced-screen-width-unset {
    width: unset;
    position: unset;
    left: unset;
    right: unset;
    margin-left: unset;
    margin-right: unset;
}

.vf-container {
    position: relative;
    top: 0;
    left: 0;
    overflow: hidden;
    width: 100%;
    height: 100%;
}
.vf-container img {
    position: absolute;
    left: 0;
    top: 0;
    margin: 0;
    display: block;
    /* fill and maintain aspect ratio */
    width: auto; height: auto;
    min-width: 100%; min-height: 100%;
    max-height: none; max-width: none;
}
