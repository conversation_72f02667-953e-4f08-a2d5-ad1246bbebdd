<?xml version="1.0" encoding="utf-8"?>
<html
  data-namespace-typo3-fluid="true"
  xmlns="http://www.w3.org/1999/xhtml"
  xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
  xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
  xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="area" label="Layout (Bereich)" description="Inhaltselement um einen Bereich im Grid für beliebig viele Inhalte zu erstellen.">
    <flux:form.option.group value="tknucera-layout-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-area.svg" />

    <flux:form.sheet name="settings.colStart"
                     label="Von Spalte"
                     description="Starte Bereich bei Spalte ... für Desktop, Tablet und/oder Mobile">
      <flux:field.select name="desktopColStart"
                         label="Desktop"
                         items="{
        0:{0:'1', 1:'lg:col-start-1'},
        1:{0:'2', 1:'lg:col-start-2'},
        2:{0:'3', 1:'lg:col-start-3'},
        3:{0:'4', 1:'lg:col-start-4'},
        4:{0:'5', 1:'lg:col-start-5'},
        5:{0:'6', 1:'lg:col-start-6'},
        6:{0:'7', 1:'lg:col-start-7'},
        7:{0:'8', 1:'lg:col-start-8'},
        8:{0:'9', 1:'lg:col-start-9'},
        9:{0:'10', 1:'lg:col-start-10'},
        10:{0:'11', 1:'lg:col-start-11'},
        11:{0:'12', 1:'lg:col-start-12'}
        }" />
      <flux:field.select name="tabletColStart"
                         label="Tablet"
                         items="{
        0:{0:'1', 1:'md:col-start-1'},
        1:{0:'2', 1:'md:col-start-2'},
        2:{0:'3', 1:'md:col-start-3'},
        3:{0:'4', 1:'md:col-start-4'},
        4:{0:'5', 1:'md:col-start-5'},
        5:{0:'6', 1:'md:col-start-6'},
        6:{0:'7', 1:'md:col-start-7'},
        7:{0:'8', 1:'md:col-start-8'}
        }" />
      <flux:field.select name="mobileColStart"
                         label="Mobile"
                         items="{
        0:{0:'1', 1:'col-start-1'},
        1:{0:'2', 1:'col-start-2'},
        2:{0:'3', 1:'col-start-3'},
        3:{0:'4', 1:'col-start-4'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.colEnd"
                     label="Bis Spalte"
                     description="Stoppe Bereich bei Spalte ...">
      <flux:field.select name="desktopColEnd"
                         label="Desktop"
                         items="{
        0:{0:'1', 1:'lg:col-end-2'},
        1:{0:'2', 1:'lg:col-end-3'},
        2:{0:'3', 1:'lg:col-end-4'},
        3:{0:'4', 1:'lg:col-end-5'},
        4:{0:'5', 1:'lg:col-end-6'},
        5:{0:'6', 1:'lg:col-end-7'},
        6:{0:'7', 1:'lg:col-end-8'},
        7:{0:'8', 1:'lg:col-end-9'},
        8:{0:'9', 1:'lg:col-end-10'},
        9:{0:'10', 1:'lg:col-end-11'},
        10:{0:'11', 1:'lg:col-end-12'},
        11:{0:'12', 1:'lg:col-end-13'}
        }" />
      <flux:field.select name="tabletColEnd"
                         label="Tablet"
                         items="{
        0:{0:'1', 1:'md:col-end-2'},
        1:{0:'2', 1:'md:col-end-3'},
        2:{0:'3', 1:'md:col-end-4'},
        3:{0:'4', 1:'md:col-end-5'},
        4:{0:'5', 1:'md:col-end-6'},
        5:{0:'6', 1:'md:col-end-7'},
        6:{0:'7', 1:'md:col-end-8'},
        7:{0:'8', 1:'md:col-end-9'}
        }" />
      <flux:field.select name="mobileColEnd"
                         label="Mobile"
                         items="{
        0:{0:'1', 1:'col-end-2'},
        1:{0:'2', 1:'col-end-3'},
        2:{0:'3', 1:'col-end-4'},
        3:{0:'4', 1:'col-end-5'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.rowStart"
                     label="von Reihe"
                     description="Start bei Zeile ...">
      <flux:field.select name="desktopRowStart"
                         label="Desktop"
                         items="{
        0:{0:'Automatisch', 1:'lg:row-start-auto'},
        1:{0:'1', 1:'lg:row-start-1'},
        2:{0:'2', 1:'lg:row-start-2'},
        3:{0:'3', 1:'lg:row-start-3'},
        4:{0:'4', 1:'lg:row-start-4'},
        5:{0:'5', 1:'lg:row-start-5'},
        6:{0:'6', 1:'lg:row-start-6'},
        7:{0:'7', 1:'lg:row-start-7'},
        8:{0:'8', 1:'lg:row-start-8'},
        9:{0:'9', 1:'lg:row-start-9'},
        10:{0:'10', 1:'lg:row-start-10'}
        }" />
      <flux:field.select name="tabletRowStart"
                         label="Tablet"
                         items="{
        0:{0:'Automatisch', 1:'md:row-start-auto'},
        1:{0:'1', 1:'md:row-start-1'},
        2:{0:'2', 1:'md:row-start-2'},
        3:{0:'3', 1:'md:row-start-3'},
        4:{0:'4', 1:'md:row-start-4'},
        5:{0:'5', 1:'md:row-start-5'},
        6:{0:'6', 1:'md:row-start-6'},
        7:{0:'7', 1:'md:row-start-7'},
        8:{0:'8', 1:'md:row-start-8'},
        9:{0:'9', 1:'md:row-start-9'},
        10:{0:'10', 1:'md:row-start-10'}
        }" />
      <flux:field.select name="mobileRowStart"
                         label="Mobile"
                         items="{
        0:{0:'Automatisch', 1:'row-start-auto'},
        1:{0:'1', 1:'row-start-1'},
        2:{0:'2', 1:'row-start-2'},
        3:{0:'3', 1:'row-start-3'},
        4:{0:'4', 1:'row-start-4'},
        5:{0:'5', 1:'row-start-5'},
        6:{0:'6', 1:'row-start-6'},
        7:{0:'7', 1:'row-start-7'},
        8:{0:'8', 1:'row-start-8'},
        9:{0:'9', 1:'row-start-9'},
        10:{0:'10', 1:'row-start-10'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.rowEnd"
                     label="bis Reihe"
                     description="Ende bei Zeile ...">
      <flux:field.select name="desktopRowEnd"
                         label="Desktop"
                         items="{
        0:{0:'Automatisch', 1:'lg:row-end-auto'},
        1:{0:'1', 1:'lg:row-end-2'},
        2:{0:'2', 1:'lg:row-end-3'},
        3:{0:'3', 1:'lg:row-end-4'},
        4:{0:'4', 1:'lg:row-end-5'},
        5:{0:'5', 1:'lg:row-end-6'},
        6:{0:'6', 1:'lg:row-end-7'},
        7:{0:'7', 1:'lg:row-end-8'},
        8:{0:'8', 1:'lg:row-end-9'},
        9:{0:'9', 1:'lg:row-end-10'}
        }" />
      <flux:field.select name="tabletRowEnd"
                         label="Tablet"
                         items="{
        0:{0:'Automatisch', 1:'md:row-end-auto'},
        1:{0:'1', 1:'md:row-end-2'},
        2:{0:'2', 1:'md:row-end-3'},
        3:{0:'3', 1:'md:row-end-4'},
        4:{0:'4', 1:'md:row-end-5'},
        5:{0:'5', 1:'md:row-end-6'},
        6:{0:'6', 1:'md:row-end-7'},
        7:{0:'7', 1:'md:row-end-8'},
        8:{0:'8', 1:'md:row-end-9'},
        9:{0:'9', 1:'md:row-end-10'}
        }" />
      <flux:field.select name="mobileRowEnd"
                         label="Mobile"
                         items="{
        0:{0:'Automatisch', 1:'row-end-auto'},
        1:{0:'1', 1:'row-end-2'},
        2:{0:'2', 1:'row-end-3'},
        3:{0:'3', 1:'row-end-4'},
        4:{0:'4', 1:'row-end-5'},
        5:{0:'5', 1:'row-end-6'},
        6:{0:'6', 1:'row-end-7'},
        7:{0:'7', 1:'row-end-8'},
        8:{0:'8', 1:'row-end-9'},
        9:{0:'9', 1:'row-end-10'}
        }" />
    </flux:form.sheet>


    <flux:form.sheet name="settings.zIndex"
                     label="Ebene"
                     description="Ebene (index)">
      <flux:field.select name="zIndexDesktop"
                         label="Desktop"
                         items="{
        0:{0:'Automatisch', 1:'lg:z-auto'},
        1:{0:'0', 1:'lg:z-0'},
        2:{0:'1', 1:'lg:z-1'},
        3:{0:'2', 1:'lg:z-2'},
        4:{0:'3', 1:'lg:z-3'},
        5:{0:'4', 1:'lg:z-4'},
        6:{0:'5', 1:'lg:z-5'},
        7:{0:'6', 1:'lg:z-6'},
        8:{0:'3', 1:'lg:z-7'},
        9:{0:'4', 1:'lg:z-8'},
        10:{0:'5', 1:'lg:z-9'},
        11:{0:'6', 1:'lg:z-10'}
        }" />
      <flux:field.select name="zIndexTablet"
                         label="Tablet"
                         items="{
        0:{0:'Automatisch', 1:'md:z-auto'},
        1:{0:'0', 1:'md:z-0'},
        2:{0:'1', 1:'md:z-1'},
        3:{0:'2', 1:'md:z-2'},
        4:{0:'3', 1:'md:z-3'},
        5:{0:'4', 1:'md:z-4'},
        6:{0:'5', 1:'md:z-5'},
        7:{0:'6', 1:'md:z-6'},
        8:{0:'3', 1:'md:z-7'},
        9:{0:'4', 1:'md:z-8'},
        10:{0:'5', 1:'md:z-9'},
        11:{0:'6', 1:'md:z-10'}
        }" />
      <flux:field.select name="zIndexMobile"
                         label="Mobile"
                         items="{
        0:{0:'Automatisch', 1:'z-auto'},
        1:{0:'0', 1:'lg:z-0'},
        2:{0:'1', 1:'z-1'},
        3:{0:'2', 1:'z-2'},
        4:{0:'3', 1:'z-3'},
        5:{0:'4', 1:'z-4'},
        6:{0:'5', 1:'z-5'},
        7:{0:'6', 1:'z-6'},
        8:{0:'3', 1:'z-7'},
        9:{0:'4', 1:'z-8'},
        10:{0:'5', 1:'z-9'},
        11:{0:'6', 1:'z-10'}
        }" />
    </flux:form.sheet>

    <flux:form.sheet name="settings.flexibleCols"
                     label="Spalten"
                     description="Automatische Aufteilung der Inhaltselemente in die gewählten Spalten">
      <flux:field.select name="flexibleColsDesktop"
                         label="Desktop"
                         items="{
        0:{0:'Automatisch', 1:''},
        1:{0:'2 Spalten', 1:'lg:grid lg:grid-flow-row-dense lg:gap-8 lg:grid-cols-2'},
        2:{0:'3 Spalten', 1:'lg:grid lg:grid-flow-row-dense lg:gap-8 lg:grid-cols-3'},
        3:{0:'4 Spalten', 1:'lg:grid lg:grid-flow-row-dense lg:gap-8 lg:grid-cols-4'},
        4:{0:'5 Spalten', 1:'lg:grid lg:grid-flow-row-dense lg:gap-8 lg:grid-cols-5'}
        }" />
      <flux:field.select name="flexibleColsTablet"
                         label="Tablet"
                         items="{
        0:{0:'Automatisch', 1:''},
        1:{0:'2 Spalten', 1:'md:grid md:grid-flow-row-dense md:gap-6 md:grid-cols-2'},
        2:{0:'3 Spalten', 1:'md:grid md:grid-flow-row-dense md:gap-6 md:grid-cols-3'},
        3:{0:'4 Spalten', 1:'md:grid md:grid-flow-row-dense md:gap-6 md:grid-cols-4'},
        4:{0:'5 Spalten', 1:'md:grid md:grid-flow-row-dense md:gap-6 md:grid-cols-5'}
        }" />
      <flux:field.select name="flexibleColsMobile"
                         label="Mobile"
                         items="{
        0:{0:'Automatisch', 1:'grid gap-4'},
        1:{0:'2 Spalten', 1:'grid grid-flow-row-dense gap-4 grid-cols-2'},
        2:{0:'3 Spalten', 1:'grid grid-flow-row-dense gap-4 grid-cols-3'},
        3:{0:'4 Spalten', 1:'grid grid-flow-row-dense gap-4 grid-cols-4'},
        4:{0:'5 Spalten', 1:'grid grid-flow-row-dense gap-4 grid-cols-5'}
        }" />
    </flux:form.sheet>

    <flux:form.sheet name="settings.alignX"
                     label="↔"
                     description="Horizontale Ausrichtung">
      <flux:field.select name="alignXDesktop"
                         label="Desktop"
                         items="{
        0:{0:'links', 1:'lg:justify-left'},
        1:{0:'mitte', 1:'lg:justify-center'},
        2:{0:'rechts', 1:'lg:justify-end'},
        3:{0:'volle Breite', 1:'lg:w-full'}
        }" />
      <flux:field.select name="alignXTablet"
                         label="Tablet"
                         items="{
        0:{0:'links', 1:'md:justify-left'},
        1:{0:'mitte', 1:'md:justify-center'},
        2:{0:'rechts', 1:'md:justify-end'},
        3:{0:'volle Breite', 1:'md:w-full'}
        }" />
      <flux:field.select name="alignXMobile"
                         label="Mobile"
                         items="{
        0:{0:'links', 1:'justify-left'},
        1:{0:'mitte', 1:'justify-center'},
        2:{0:'rechts', 1:'justify-end'},
        3:{0:'volle Breite', 1:'w-full'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.alignY"
                     label="↕"
                     description="Vertikale Ausrichtung">
      <flux:field.select name="alignYDesktop"
                         label="Desktop"
                         items="{
        0:{0:'oben', 1:'lg:items-start'},
        1:{0:'mitte', 1:'lg:items-center'},
        2:{0:'unten', 1:'lg:items-end'},
        3:{0:'volle Höhe', 1:'lg:items-stretch'}
        }" />
      <flux:field.select name="alignYTablet"
                         label="Tablet"
                         items="{
        0:{0:'oben', 1:'md:items-start'},
        1:{0:'mitte', 1:'md:items-center'},
        2:{0:'unten', 1:'md:items-end'},
        3:{0:'volle Höhe', 1:'md:items-stretch'}
        }" />
      <flux:field.select name="alignYMobile"
                         label="Mobile"
                         items="{
        0:{0:'oben', 1:'items-start'},
        1:{0:'mitte', 1:'items-center'},
        2:{0:'unten', 1:'items-end'},
        3:{0:'volle Höhe', 1:'items-stretch'}
        }" />
    </flux:form.sheet>

    <flux:form.sheet name="settings.marginLeft"
                     label="←"
                     description="Abstand links">
      <flux:field.select name="marginLeftDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:ml-0'},
        1:{0:'-3', 1:'lg:-ml-12'},
        2:{0:'-2', 1:'lg:-ml-8'},
        3:{0:'-1', 1:'lg:-ml-4'},
        4:{0:'1', 1:'lg:ml-4'},
        5:{0:'2', 1:'lg:ml-8'},
        6:{0:'3', 1:'lg:ml-12'}
        }" />
      <flux:field.select name="marginLeftTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:ml-0'},
        1:{0:'-3', 1:'md:-ml-12'},
        2:{0:'-2', 1:'md:-ml-8'},
        3:{0:'-1', 1:'md:-ml-4'},
        4:{0:'1', 1:'md:ml-4'},
        5:{0:'2', 1:'md:ml-8'},
        6:{0:'3', 1:'md:ml-12'}
        }" />
      <flux:field.select name="marginLeftMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'ml-0'},
        1:{0:'-3', 1:'-ml-12'},
        2:{0:'-2', 1:'-ml-8'},
        3:{0:'-1', 1:'-ml-4'},
        4:{0:'1', 1:'ml-4'},
        5:{0:'2', 1:'ml-8'},
        6:{0:'3', 1:'ml-12'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.marginTop"
                     label="↑"
                     description="Abstand oben">
      <flux:field.select name="marginTopDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:mt-0'},
        1:{0:'-3', 1:'lg:-mt-12'},
        2:{0:'-2', 1:'lg:-mt-8'},
        3:{0:'-1', 1:'lg:-mt-4'},
        4:{0:'1', 1:'lg:mt-4'},
        5:{0:'2', 1:'lg:mt-8'},
        6:{0:'3', 1:'lg:mt-12'}
        }" />
      <flux:field.select name="marginTopTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:mt-0'},
        1:{0:'-3', 1:'md:-mt-12'},
        2:{0:'-2', 1:'md:-mt-8'},
        3:{0:'-1', 1:'md:-mt-4'},
        4:{0:'1', 1:'md:mt-4'},
        5:{0:'2', 1:'md:mt-8'},
        6:{0:'3', 1:'md:mt-12'}
        }" />
      <flux:field.select name="marginTopMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'mt-0'},
        1:{0:'-3', 1:'-mt-12'},
        2:{0:'-2', 1:'-mt-8'},
        3:{0:'-1', 1:'-mt-4'},
        4:{0:'1', 1:'mt-4'},
        5:{0:'2', 1:'mt-8'},
        6:{0:'3', 1:'mt-12'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.marginRight"
                     label="→"
                     description="Abstand rechts">
      <flux:field.select name="marginRightDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:mr-0'},
        1:{0:'-3', 1:'lg:-mr-12'},
        2:{0:'-2', 1:'lg:-mr-8'},
        3:{0:'-1', 1:'lg:-mr-4'},
        4:{0:'1', 1:'lg:mr-4'},
        5:{0:'2', 1:'lg:mr-8'},
        6:{0:'3', 1:'lg:mr-12'}
        }" />
      <flux:field.select name="marginRightTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:mr-0'},
        1:{0:'-3', 1:'md:-mr-12'},
        2:{0:'-2', 1:'md:-mr-8'},
        3:{0:'-1', 1:'md:-mr-4'},
        4:{0:'1', 1:'md:mr-4'},
        5:{0:'2', 1:'md:mr-8'},
        6:{0:'3', 1:'md:mr-12'}
        }" />
      <flux:field.select name="marginRightMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'mr-0'},
        1:{0:'-3', 1:'-mr-12'},
        2:{0:'-2', 1:'-mr-8'},
        3:{0:'-1', 1:'-mr-4'},
        4:{0:'1', 1:'mr-4'},
        5:{0:'2', 1:'mr-8'},
        6:{0:'3', 1:'mr-12'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.marginBottom"
                     label="↓"
                     description="Abstand unten">
      <flux:field.select name="marginBottomDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:mb-0'},
        1:{0:'-3', 1:'lg:-mb-12'},
        2:{0:'-2', 1:'lg:-mb-8'},
        3:{0:'-1', 1:'lg:-mb-4'},
        4:{0:'1', 1:'lg:mb-4'},
        5:{0:'2', 1:'lg:mb-8'},
        6:{0:'3', 1:'lg:mb-12'}
        }" />
      <flux:field.select name="marginBottomTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:mb-0'},
        1:{0:'-3', 1:'md:-mb-12'},
        2:{0:'-2', 1:'md:-mb-8'},
        3:{0:'-1', 1:'md:-mb-4'},
        4:{0:'1', 1:'md:mb-4'},
        5:{0:'2', 1:'md:mb-8'},
        6:{0:'3', 1:'md:mb-12'}
        }" />
      <flux:field.select name="marginBottomMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'mb-0'},
        1:{0:'-3', 1:'-mb-12'},
        2:{0:'-2', 1:'-mb-8'},
        3:{0:'-1', 1:'-mb-4'},
        4:{0:'1', 1:'mb-4'},
        5:{0:'2', 1:'mb-8'},
        6:{0:'3', 1:'mb-12'}
        }" />
    </flux:form.sheet>

    <flux:grid>
      <flux:grid.row>
        <flux:grid.column label="Area" name="area" colPos="0"/>
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgba(92, 166, 210, 1);height:10px;"></div>
</f:section>

<f:section name="Main">
  <div id="c{f:render(partial: 'Data/RecordUid', section: 'main', arguments: _all) -> f:spaceless()}" class="flex
  {alignXMobile} {alignXTablet} {alignXDesktop}
  {alignYMobile} {alignYTablet} {alignYDesktop}
  {mobileColStart} {tabletColStart} {desktopColStart}
  {mobileColEnd} {tabletColEnd} {desktopColEnd}
  {mobileRowStart} {tabletRowStart} {desktopRowStart}
  {mobileRowEnd} {tabletRowEnd} {desktopRowEnd}
  {marginLeftMobile} {marginLeftTablet} {marginLeftDesktop}
  {marginTopMobile} {marginTopTablet} {marginTopDesktop}
  {marginRightMobile} {marginRightTablet} {marginRightDesktop}
  {marginBottomMobile} {marginBottomTablet} {marginBottomDesktop}
  {zIndexMobile} {zIndexTablet} {zIndexDesktop}" data-area-title="{record.header}">
    <f:if condition="{alignYDesktop} == 'lg:items-stretch'">
      <f:variable name="heightClassDesktop" value="lg:h-full"/>
    </f:if>
    <f:if condition="{alignYTablet} == 'md:items-stretch'">
      <f:variable name="heightClassTablet" value="md:h-full"/>
    </f:if>
    <f:if condition="{alignYMobile} == 'items-stretch'">
      <f:variable name="heightClassMobile" value="h-full"/>
    </f:if>

    <f:if condition="{alignXDesktop} == 'lg:w-full'">
      <f:variable name="widthClassDesktop" value="lg:w-full"/>
    </f:if>
    <f:if condition="{alignXTablet} == 'md:w-full'">
      <f:variable name="widthClassTablet" value="md:w-full"/>
    </f:if>
    <f:if condition="{alignXMobile} == 'w-full'">
      <f:variable name="widthClassMobile" value="w-full"/>
    </f:if>

    <div class="relative {flexibleColsMobile} {flexibleColsTablet} {flexibleColsDesktop} {widthClassMobile} {widthClassTablet} {widthClassDesktop} {heightClassMobile} {heightClassTablet} {heightClassDesktop}">
      <f:for each="{flux:content.get(area: 'area')}" as="contentElement">
        <div>
          <f:format.raw>{contentElement}</f:format.raw>
        </div>
      </f:for>
    </div>
  </div>
</f:section>

</html>
