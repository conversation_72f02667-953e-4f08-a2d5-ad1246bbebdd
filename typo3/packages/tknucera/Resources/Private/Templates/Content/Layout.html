<?xml version="1.0" encoding="utf-8"?>
<html
        data-namespace-typo3-fluid="true"
        xmlns="http://www.w3.org/1999/xhtml"
        xmlns:f="http://typo3.org/ns/TYPO3/Fluid/ViewHelpers"
        xmlns:flux="http://typo3.org/ns/FluidTYPO3/Flux/ViewHelpers"
        xmlns:v="http://typo3.org/ns/FluidTYPO3/Vhs/ViewHelpers"
>
<f:layout name="ContentElement" />

<f:section name="Configuration">
  <flux:form id="layout" label="Layout (Rahmen)" description="Rahmen mit Grid (Desktop: 12, Tablet: 8, Mobile: 4 Spalten) und Einstellungen wie Abstände und Hintergrund">
    <flux:form.option.group value="tknucera-layout-elements" />
    <flux:form.option.icon value="EXT:tknucera/Resources/Public/Icons/Content/content-layout.svg" />

    <flux:form.sheet name="settings.marginTop"
                     label="Abstand vor"
                     description="Abstand vor dem Element">
      <flux:field.select name="marginTopDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:mt-0'},
        1:{0:'1', 1:'lg:mt-4'},
        2:{0:'2', 1:'lg:mt-8'},
        3:{0:'3', 1:'lg:mt-12'}
        }" />
      <flux:field.select name="marginTopTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:mt-0'},
        1:{0:'1', 1:'md:mt-4'},
        2:{0:'2', 1:'md:mt-8'},
        3:{0:'3', 1:'md:mt-12'}
        }" />
      <flux:field.select name="marginTopMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'mt-0'},
        1:{0:'1', 1:'mt-4'},
        2:{0:'2', 1:'mt-8'},
        3:{0:'3', 1:'mt-12'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.marginBottom"
                     label="Abstand nach"
                     description="Abstand nach dem Element">
      <flux:field.select name="marginBottomDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:mb-0'},
        1:{0:'1', 1:'lg:mb-4'},
        2:{0:'2', 1:'lg:mb-8'},
        3:{0:'3', 1:'lg:mb-12'}
        }" />
      <flux:field.select name="marginBottomTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:mb-0'},
        1:{0:'1', 1:'md:mb-4'},
        2:{0:'2', 1:'md:mb-8'},
        3:{0:'3', 1:'md:mb-12'}
        }" />
      <flux:field.select name="marginBottomMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'mb-0'},
        1:{0:'1', 1:'mb-4'},
        2:{0:'2', 1:'mb-8'},
        3:{0:'3', 1:'mb-12'}
        }" />
    </flux:form.sheet>

    <flux:form.sheet name="settings.paddingTop"
                     label="Abstand oben"
                     description="Abstand von oben (innerhalb)">
      <flux:field.select name="paddingTopDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:pt-0'},
        1:{0:'1', 1:'lg:pt-4'},
        2:{0:'2', 1:'lg:pt-8'},
        3:{0:'3', 1:'lg:pt-12'}
        }" />
      <flux:field.select name="paddingTopTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:pt-0'},
        1:{0:'1', 1:'md:pt-4'},
        2:{0:'2', 1:'md:pt-8'},
        3:{0:'3', 1:'md:pt-12'}
        }" />
      <flux:field.select name="paddingTopMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'pt-0'},
        1:{0:'1', 1:'pt-4'},
        2:{0:'2', 1:'pt-8'},
        3:{0:'3', 1:'pt-12'}
        }" />
    </flux:form.sheet>
    <flux:form.sheet name="settings.paddingBottom"
                     label="Abstand unten"
                     description="Abstand von unten (innerhalb)">
      <flux:field.select name="paddingBottomDesktop"
                         label="Desktop"
                         items="{
        0:{0:'kein Abstand', 1:'lg:pb-0'},
        1:{0:'1', 1:'lg:pb-4'},
        2:{0:'2', 1:'lg:pb-8'},
        3:{0:'3', 1:'lg:pb-12'}
        }" />
      <flux:field.select name="paddingBottomTablet"
                         label="Tablet"
                         items="{
        0:{0:'kein Abstand', 1:'md:pb-0'},
        1:{0:'1', 1:'md:pb-4'},
        2:{0:'2', 1:'md:pb-8'},
        3:{0:'3', 1:'md:pb-12'}
        }" />
      <flux:field.select name="paddingBottomMobile"
                         label="Mobile"
                         items="{
        0:{0:'kein Abstand', 1:'pb-0'},
        1:{0:'1', 1:'pb-4'},
        2:{0:'2', 1:'pb-8'},
        3:{0:'3', 1:'pb-12'}
        }" />
    </flux:form.sheet>

    <flux:form.sheet name="settings.background"
                     label="Hintergrund"
                     description="Einstellungen zum Hintergrund (Farbe)">
      <flux:field.select name="backgroundColor"
                         label="Hintergrundfarbe"
                         items="{
        0:{0:'keine Farbe', 1:''}
        }" />
    </flux:form.sheet>

    <flux:grid>
      <flux:grid.row>
        <flux:grid.column label="Layout" name="layout" colPos="0">
          <flux:form.variable name="allowedContentTypes" value="tknucera_area" />
        </flux:grid.column>
      </flux:grid.row>
    </flux:grid>
  </flux:form>
</f:section>

<f:section name="Preview">
  <div style="background: rgba(0, 87, 139, 1);height: 0.5rem;"></div>
</f:section>

<f:section name="Main">
  <f:variable name="gridBaseClasses" value="grid grid-cols-4 gap-4 md:grid-cols-8 md:gap-6 lg:grid-cols-12 lg:gap-8 px-4 md:px-8 lg:px-12" />
  <div id="c{f:render(partial: 'Data/RecordUid', section: 'main', arguments: _all) -> f:spaceless()}" class="{gridBaseClasses}
  {paddingTopMobile} {paddingTopTablet} {paddingTopDesktop}
  {paddingBottomMobile} {paddingBottomTablet} {paddingBottomDesktop}
  {marginTopMobile} {marginTopTablet} {marginTopDesktop}
  {marginBottomMobile} {marginBottomTablet} {marginBottomDesktop}
  {backgroundColor}">
    <f:for each="{flux:content.get(area: 'layout')}" as="contentElement">
      <f:format.raw>{contentElement}</f:format.raw>
    </f:for>
  </div>
  <f:comment>
    All the required classes, that could be set
    <div class="hidden">
      <div class="
    pt-4
    pt-8
    pt-12
    md:pt-4
    md:pt-8
    md:pt-12
    lg:pt-4
    lg:pt-8
    lg:pt-12
    pb-4
    pb-8
    pb-12
    md:pb-4
    md:pb-8
    md:pb-12
    lg:pb-4
    lg:pb-8
    lg:pb-12
    mt-4
    mt-8
    mt-12
    md:mt-4
    md:mt-8
    md:mt-12
    lg:mt-4
    lg:mt-8
    lg:mt-12
    mb-4
    mb-8
    mb-12
    md:mb-4
    md:mb-8
    md:mb-12
    lg:mb-4
    lg:mb-8
    lg:mb-12

    w-full
    w-auto
    md:w-full
    md:w-auto
    lg:w-auto
    lg:w-full
    aspect-auto
    aspect-video
    aspect-square
    aspect-photo_landscape
    aspect-photo_portrait
    md:aspect-auto
    md:aspect-video
    md:aspect-square
    md:aspect-photo_landscape
    md:aspect-photo_portrait
    lg:aspect-auto
    lg:aspect-video
    lg:aspect-square
    lg:aspect-photo_landscape
    lg:aspect-photo_portrait
                content-start
                content-center
                content-end
                justify-start
                justify-center
                justify-end
                items-start
                items-center
                items-end
                items-stretch
                md:justify-start
                md:justify-center
                md:justify-end
                md:items-start
                md:items-center
                md:items-end
                md:items-stretch
                lg:justify-start
                lg:justify-center
                lg:justify-end
                lg:items-start
                lg:items-center
                lg:items-end
                lg:items-stretch
                col-start-1
                col-start-2
                col-start-3
                col-start-4
                col-end-2
                col-end-3
                col-end-4
                col-end-5
                md:col-start-1
                md:col-start-2
                md:col-start-3
                md:col-start-4
                md:col-start-5
                md:col-start-6
                md:col-start-7
                md:col-start-8
                md:col-end-2
                md:col-end-3
                md:col-end-4
                md:col-end-5
                md:col-end-6
                md:col-end-7
                md:col-end-8
                md:col-end-9
                lg:col-start-1
                lg:col-start-2
                lg:col-start-3
                lg:col-start-4
                lg:col-start-5
                lg:col-start-6
                lg:col-start-7
                lg:col-start-8
                lg:col-start-9
                lg:col-start-10
                lg:col-start-11
                lg:col-start-12
                lg:col-end-2
                lg:col-end-3
                lg:col-end-4
                lg:col-end-5
                lg:col-end-6
                lg:col-end-7
                lg:col-end-8
                lg:col-end-9
                lg:col-end-10
                lg:col-end-11
                lg:col-end-12
                lg:col-end-13
row-end-1
row-end-2
row-end-3
row-end-4
row-end-5
row-end-6
row-end-7
row-end-8
row-end-9
row-end-10
md:row-end-1
md:row-end-2
md:row-end-3
md:row-end-4
md:row-end-5
md:row-end-6
md:row-end-7
md:row-end-8
md:row-end-9
md:row-end-10
lg:row-end-1
lg:row-end-2
lg:row-end-3
lg:row-end-4
lg:row-end-5
lg:row-end-6
lg:row-end-7
lg:row-end-8
lg:row-end-9
lg:row-end-10
row-end-1
row-end-2
row-end-3
row-end-4
row-end-5
row-end-6
row-end-7
row-end-8
row-end-9
row-end-10
row-end-auto
row-start-auto
md:row-end-1
md:row-end-2
md:row-end-3
md:row-end-4
md:row-end-5
md:row-end-6
md:row-end-7
md:row-end-8
md:row-end-9
md:row-end-10
md:row-end-auto
md:row-start-auto
lg:row-end-1
lg:row-end-2
lg:row-end-3
lg:row-end-4
lg:row-end-5
lg:row-end-6
lg:row-end-7
lg:row-end-8
lg:row-end-9
lg:row-end-10
lg:row-end-auto
lg:row-start-auto
mt-4
mt-8
mt-12
md:mt-4
md:mt-8
md:mt-12
lg:mt-4
lg:mt-8
lg:mt-12
-mt-4
-mt-8
-mt-12
md:-mt-4
md:-mt-8
md:-mt-12
lg:-mt-4
lg:-mt-8
lg:-mt-12
ml-4
ml-8
ml-12
md:ml-4
md:ml-8
md:ml-12
lg:ml-4
lg:ml-8
lg:ml-12
-ml-4
-ml-8
-ml-12
md:-ml-4
md:-ml-8
md:-ml-12
lg:-ml-4
lg:-ml-8
lg:-ml-12
mb-4
mb-8
mb-12
md:mb-4
md:mb-8
md:mb-12
lg:mb-4
lg:mb-8
lg:mb-12
-mb-4
-mb-8
-mb-12
md:-mb-4
md:-mb-8
md:-mb-12
lg:-mb-4
lg:-mb-8
lg:-mb-12
mr-4
mr-8
mr-12
md:mr-4
md:mr-8
md:mr-12
lg:mr-4
lg:mr-8
lg:mr-12
-mr-4
-mr-8
-mr-12
md:-mr-4
md:-mr-8
md:-mr-12
lg:-mr-4
lg:-mr-8
lg:-mr-12
mb-0
md:mb-0
lg:mb-0
ml-0
md:ml-0
lg:ml-0
mr-0
md:mr-0
lg:mr-0
mt-0
md:mt-0
lg:mt-0
z-auto
z-0
z-1
z-2
z-3
z-4
z-5
z-6
z-7
z-8
z-9
z-10
md:z-auto
md:z-0
md:z-1
md:z-2
md:z-3
md:z-4
md:z-5
md:z-6
md:z-7
md:z-8
md:z-9
md:z-10
lg:z-auto
lg:z-0
lg:z-1
lg:z-2
lg:z-3
lg:z-4
lg:z-5
lg:z-6
lg:z-7
lg:z-8
lg:z-9
lg:z-10"></div>
    </div>
  </f:comment>
</f:section>

</html>
