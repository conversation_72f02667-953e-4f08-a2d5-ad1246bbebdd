const accordeon = (element) => {
    let config = {
        context: '.accordeon',
        head: '.accordeonHead',
        body: '.accordeonBody'
    };
    let fn = {
        showContent: (target) => {
            target.classList.add('activeContent');
            target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
            let timer = window.setTimeout(() => {
                target.style.maxHeight = '';
                window.clearTimeout(timer);
            }, 300);
        },
        hideContent: (target) => {
            target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
            let timer = window.setTimeout(() => {
                target.classList.remove('activeContent');
                target.style.maxHeight = '0px';
                window.clearTimeout(timer);
            }, 10);
        },
        saveCurrentHeight: (target) => {
            target.setAttribute('originalHeight', target.offsetHeight);
        }
    }
    if (element) {
        fn.hideContent(element.querySelector(config.body));
    } else {
        document.querySelectorAll(config.context).forEach((element, index) => {
            element.querySelectorAll(config.body).forEach((target, index) => {
                fn.saveCurrentHeight(target);
                fn.hideContent(target);
            });
            element.querySelectorAll(config.head).forEach((head, index) => {
                let thisBody = head.nextElementSibling;
                head.addEventListener("click", () => {
                    if (!thisBody.classList.contains('activeContent')) {
                        fn.showContent(thisBody);
                        head.querySelector('svg').classList.add('rotate-180');
                        head.classList.replace('text-grey-100', 'text-black');
                    } else {
                        fn.hideContent(thisBody);
                        head.querySelector('svg').classList.remove('rotate-180');
                        head.classList.replace('text-black', 'text-grey-100');
                    }
                });
            });
        });
    }
};

let highlightHeadline = (element) => {
    let text = element.innerHTML.split(' '),
        hightlightedText = '';
    text.forEach((highlight, index) => {
        if (highlight === '<br>') {
            hightlightedText = hightlightedText + highlight;
        } else {
            hightlightedText = hightlightedText + '<span>' + highlight + '</span> ';
        }
        if (index === text.length - 1) {
            element.innerHTML = hightlightedText;
        }
    });
}

let highlightHeadlines = () => {
    let highlightHeadlines = document.querySelectorAll('.highlightHeadline');
    highlightHeadlines.forEach((headline, index) => {
        highlightHeadline(headline);
    });
}

const dropdown = () => {
    const dropdownWrapper = document.querySelectorAll('#dropdown');

    dropdownWrapper.forEach((element, index) => {
        const dropdownButton = element.querySelector('button');
        const dropdownList = element.querySelector('#dropdownList');
        const dropdownChevron = element.querySelector('#dropdownChevron');

        dropdownButton.addEventListener('click', () => {
            dropdownList.classList.toggle('hidden');
            dropdownChevron.classList.toggle('rotate-180');
        });

        document.addEventListener('click', (event) => {
            if (!event.target.closest('#dropdownButton')) {
                dropdownList.classList.add('hidden');
            }
            if (dropdownList.classList.contains('hidden')) {
                dropdownChevron.classList.remove('rotate-180');
            }
        });

        dropdownList.addEventListener('click', (event) => {
            const dropdownButtonInnerHTML = element.querySelector('#dropdownButton-innerHTML');
            dropdownButtonInnerHTML.innerHTML = event.target.innerHTML;
            dropdownList.classList.add('hidden');
    });
  });
}

const tabs = () => {
  const tabButtons = document.querySelectorAll('[role="tab"]');
  const tabPanels = document.querySelectorAll('[role="tabpanel"]');
  const tabList = document.getElementById('tablist');
  const tabWrapper = document.getElementById('tablist-wrapper');
  const prevBtn = document.getElementById('tab-prev');
  const nextBtn = document.getElementById('tab-next');
  let scrollIndex = 0;
  let isScrollable = false;

  const initTabs = () => {
    tabButtons.forEach((btn, i) => {
      if (i === 0) {
        btn.setAttribute('aria-selected', 'true');
        btn.classList.remove('text-skyGray', 'border-skyGray');
        btn.classList.add('text-white', 'border-white', 'bg-urbanGray');
      } else {
        btn.setAttribute('aria-selected', 'false');
        btn.classList.remove('text-white', 'border-white');
        btn.classList.add('text-skyGray', 'border-skyGray');
        tabPanels[i].classList.add('hidden');
      }
    });
  };
  initTabs();

  tabButtons.forEach((button, index) => {
    button.addEventListener('click', () => {
      tabButtons.forEach((btn) => {
        btn.setAttribute('aria-selected', 'false');
        btn.classList.remove('text-white', 'border-white', 'bg-urbanGray');
        btn.classList.add('text-skyGray', 'border-skyGray');
      });

      button.setAttribute('aria-selected', 'true');
      button.classList.remove('text-skyGray', 'border-skyGray');
      button.classList.add('text-white', 'border-white', 'bg-urbanGray');

      tabPanels.forEach((panel) => panel.classList.add('hidden'));
      tabPanels[index].classList.remove('hidden');
    });
  });


  const scrollTabs = (direction) => {
    if (!isScrollable) return;
    const tabWidth = tabButtons[0].offsetWidth;
    const visibleTabs = 3;
    scrollIndex += direction;
    const maxIndex = tabButtons.length - visibleTabs;
    if (scrollIndex < 0) scrollIndex = 0;
    if (scrollIndex > maxIndex) scrollIndex = maxIndex;

    tabList.style.transform = `translateX(-${scrollIndex * tabWidth}px)`;
    prevBtn.disabled = scrollIndex === 0;
    nextBtn.disabled = scrollIndex === maxIndex;
  };

  prevBtn.addEventListener('click', () => scrollTabs(-1));
  nextBtn.addEventListener('click', () => scrollTabs(1));

  const handleResize = () => {
    const mdScreen = window.matchMedia('(min-width: 768px)').matches;

    if (mdScreen) {
      tabList.style.transform = 'translateX(0)';
      tabList.style.width = 'auto';
      tabWrapper.style.overflow = 'visible';
      prevBtn.style.display = 'none';
      nextBtn.style.display = 'none';
      tabList.style.transition = 'none';
      isScrollable = false;
    } else {
      const tabWidth = tabButtons[0].offsetWidth;
      const visibleTabs = 3;
      tabList.style.width = `${tabWidth * tabButtons.length}px`;
      tabWrapper.style.overflow = 'hidden';
      tabList.style.transition = 'transform 0.3s ease';
      scrollIndex = 0;
      tabList.style.transform = 'translateX(0)';
      prevBtn.style.display = 'block';
      nextBtn.style.display = 'block';
      prevBtn.disabled = true;
      nextBtn.disabled = tabButtons.length <= visibleTabs;
      isScrollable = true;
    }
  };

  handleResize();
  window.addEventListener('resize', handleResize);
};
