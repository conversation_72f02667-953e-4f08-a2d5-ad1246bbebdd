const accordeon = (element) => {
    let config = {
        context: '.accordeon',
        head: '.accordeonHead',
        body: '.accordeonBody'
    };
    let fn = {
        showContent: (target) => {
            target.classList.add('activeContent');
            target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
            let timer = window.setTimeout(() => {
                target.style.maxHeight = '';
                window.clearTimeout(timer);
            }, 300);
        },
        hideContent: (target) => {
            target.style.maxHeight = target.getAttribute('originalHeight') + 'px';
            let timer = window.setTimeout(() => {
                target.classList.remove('activeContent');
                target.style.maxHeight = '0px';
                window.clearTimeout(timer);
            }, 10);
        },
        saveCurrentHeight: (target) => {
            target.setAttribute('originalHeight', target.offsetHeight);
        }
    }
    if (element) {
        fn.hideContent(element.querySelector(config.body));
    } else {
        document.querySelectorAll(config.context).forEach((element, index) => {
            element.querySelectorAll(config.body).forEach((target, index) => {
                fn.saveCurrentHeight(target);
                fn.hideContent(target);
            });
            element.querySelectorAll(config.head).forEach((head, index) => {
                let thisBody = head.nextElementSibling;
                head.addEventListener("click", () => {
                    if (!thisBody.classList.contains('activeContent')) {
                        fn.showContent(thisBody);
                        head.querySelector('svg').classList.add('rotate-180');
                        head.classList.replace('text-grey-100', 'text-black');
                    } else {
                        fn.hideContent(thisBody);
                        head.querySelector('svg').classList.remove('rotate-180');
                        head.classList.replace('text-black', 'text-grey-100');
                    }
                });
            });
        });
    }
};

let highlightHeadline = (element) => {
    let text = element.innerHTML.split(' '),
        hightlightedText = '';
    text.forEach((highlight, index) => {
        if (highlight === '<br>') {
            hightlightedText = hightlightedText + highlight;
        } else {
            hightlightedText = hightlightedText + '<span>' + highlight + '</span> ';
        }
        if (index === text.length - 1) {
            element.innerHTML = hightlightedText;
        }
    });
}

let highlightHeadlines = () => {
    let highlightHeadlines = document.querySelectorAll('.highlightHeadline');
    highlightHeadlines.forEach((headline, index) => {
        highlightHeadline(headline);
    });
}

const dropdown = () => {
    const dropdownWrapper = document.querySelectorAll('#dropdown');

    dropdownWrapper.forEach((element, index) => {
        const dropdownButton = element.querySelector('button');
        const dropdownList = element.querySelector('#dropdownList');
        const dropdownChevron = element.querySelector('#dropdownChevron');

        dropdownButton.addEventListener('click', () => {
            dropdownList.classList.toggle('hidden');
            dropdownChevron.classList.toggle('rotate-180');
        });

        document.addEventListener('click', (event) => {
            if (!event.target.closest('#dropdownButton')) {
                dropdownList.classList.add('hidden');
            }
            if (dropdownList.classList.contains('hidden')) {
                dropdownChevron.classList.remove('rotate-180');
            }
        });

        dropdownList.addEventListener('click', (event) => {
            const dropdownButtonInnerHTML = element.querySelector('#dropdownButton-innerHTML');
            dropdownButtonInnerHTML.innerHTML = event.target.innerHTML;
            dropdownList.classList.add('hidden');
    });
  });
}

const tabs = () => {
  const tabButtons = document.querySelectorAll('[role="tab"]');
  const tabPanels = document.querySelectorAll('[role="tabpanel"]');
  const tabList = document.getElementById('tablist');
  const tabWrapper = document.getElementById('tablist-wrapper');
  const prevBtn = document.getElementById('tab-prev');
  const nextBtn = document.getElementById('tab-next');
  let scrollIndex = 0;
  let isScrollable = false;
  const visibleTabsCount = 3;

  const initTabs = () => {
    tabButtons.forEach((btn, i) => {
      if (i === 0) {
        btn.setAttribute('aria-selected', 'true');
        btn.classList.remove('text-skyGray', 'border-skyGray');
        btn.classList.add('text-white', 'border-white', 'bg-urbanGray');
      } else {
        btn.setAttribute('aria-selected', 'false');
        btn.classList.remove('text-white', 'border-white', 'bg-urbanGray');
        btn.classList.add('text-skyGray', 'border-skyGray');
        if (tabPanels[i]) {
          tabPanels[i].classList.add('hidden');
        }
      }
    });
  };

  const activateTab = (index) => {
    tabButtons.forEach((btn, i) => {
      if (i === index) {
        btn.setAttribute('aria-selected', 'true');
        btn.classList.remove('text-skyGray', 'border-skyGray');
        btn.classList.add('text-white', 'border-white', 'bg-urbanGray');
      } else {
        btn.setAttribute('aria-selected', 'false');
        btn.classList.remove('text-white', 'border-white', 'bg-urbanGray');
        btn.classList.add('text-skyGray', 'border-skyGray');
      }
    });

    tabPanels.forEach((panel, i) => {
      if (i === index) {
        panel.classList.remove('hidden');
      } else {
        panel.classList.add('hidden');
      }
    });

    if (isScrollable) {
      ensureTabVisible(index);
    }
  };

  const ensureTabVisible = (tabIndex) => {
    const minVisibleIndex = scrollIndex;
    const maxVisibleIndex = scrollIndex + visibleTabsCount - 1;

    if (tabIndex < minVisibleIndex) {
      scrollIndex = tabIndex;
    } else if (tabIndex > maxVisibleIndex) {
      scrollIndex = Math.max(0, tabIndex - visibleTabsCount + 1);
    }

    updateTabPosition();
  };

  const updateTabPosition = () => {
    if (!isScrollable) return;

    const containerWidth = tabWrapper.offsetWidth;
    let buttonWidth = prevBtn ? prevBtn.offsetWidth : 0;

    if (buttonWidth === 0 && prevBtn) {
      buttonWidth = 40;
    }

    const availableWidth = containerWidth - (buttonWidth * 2);
    const tabWidth = Math.max(50, availableWidth / visibleTabsCount);
    const translateX = scrollIndex * tabWidth;

    tabList.style.transform = `translateX(-${translateX}px)`;
    tabList.style.marginLeft = `${buttonWidth}px`;
    tabList.style.marginRight = `${buttonWidth}px`;

    const maxScrollIndex = Math.max(0, tabButtons.length - visibleTabsCount);
    prevBtn.disabled = scrollIndex === 0;
    nextBtn.disabled = scrollIndex >= maxScrollIndex;
  };

  const scrollTabs = (direction) => {
    if (!isScrollable) return;

    const maxScrollIndex = Math.max(0, tabButtons.length - visibleTabsCount);
    scrollIndex = Math.max(0, Math.min(maxScrollIndex, scrollIndex + direction));
    updateTabPosition();
  };

  initTabs();

  tabButtons.forEach((button, index) => {
    button.addEventListener('click', () => {
      activateTab(index);
    });
  });

  if (prevBtn) prevBtn.addEventListener('click', () => scrollTabs(-1));
  if (nextBtn) nextBtn.addEventListener('click', () => scrollTabs(1));

  const handleResize = () => {
    const mdScreen = window.matchMedia('(min-width: 768px)').matches;

    if (mdScreen) {
      tabList.style.transform = 'translateX(0)';
      tabList.style.width = 'auto';
      tabList.style.marginLeft = '0';
      tabList.style.marginRight = '0';
      tabWrapper.style.overflow = 'visible';
      isScrollable = false;
      scrollIndex = 0;

      tabButtons.forEach(btn => {
        btn.style.width = 'auto';
        btn.style.minWidth = 'auto';
        btn.style.flexShrink = '';
      });
    } else {
      const containerWidth = tabWrapper.offsetWidth;
      let buttonWidth = prevBtn ? prevBtn.offsetWidth : 0;

      if (buttonWidth === 0 && prevBtn) {
        buttonWidth = 40;
      }

      const availableWidth = containerWidth - (buttonWidth * 2);
      const tabWidth = Math.max(50, availableWidth / visibleTabsCount);

      tabWrapper.style.overflow = 'hidden';
      isScrollable = true;
      scrollIndex = 0;

      tabButtons.forEach(btn => {
        btn.style.width = `${tabWidth}px`;
        btn.style.minWidth = `${tabWidth}px`;
        btn.style.flexShrink = '0';
      });

      tabList.style.width = `${tabWidth * tabButtons.length}px`;
      updateTabPosition();
    }
  };

  setTimeout(() => {
    handleResize();
  }, 10);

  window.addEventListener('resize', handleResize);
};
