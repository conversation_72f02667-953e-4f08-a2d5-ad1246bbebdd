
let uiLibrary = {
    importTheTemplates: async (callback) => {
        let sections = document.querySelectorAll('.corporate_section'),
            templateFiles = [];
        sections.forEach((section) => {
            let sectionInfo = JSON.parse(section.dataset.sectionInfo);
            if (sectionInfo.templates !== '') {
                templateFiles.push(sectionInfo.templates);
            }
        });
        let myHeaders = new Headers();
        myHeaders.append('pragma', 'no-cache');
        myHeaders.append('cache-control', 'no-cache');

        let myInit = {
            cache: "reload",
            method: 'GET',
            headers: myHeaders,
        };
        try {
            const promises = templateFiles.map(url => fetch(url, myInit));
            const responses = await Promise.all(promises);
            const responseHTML = await Promise.all(responses.map(r => r.text()));
            responseHTML.forEach((response,index) => {
                let _context = document.createElement('template');
                _context.innerHTML = response;
                let templates = _context.content.querySelectorAll('template');
                templates.forEach((template, index) => {
                    document.body.append(template);
                });
                if (index === responseHTML.length - 1) {
                    callback();
                }
            });
        } catch (err) {
            throw err;
        }
    },
    getTemplateContent: (templateId) => {
        let content = '';
        //get the imported document in doc:
        if (document.querySelector('#' + templateId) !== null) {
            content = document.querySelector('#' + templateId).innerHTML;
        }
        return content;
    },
    showCodePreview: (templateId, button) => {
        if (button.classList.contains('activeCodeView')) {
            uiLibrary.closeCodeViewer();
            button.classList.remove('activeCodeView');
        } else {
            let content = uiLibrary.getTemplateContent(templateId),
                context = button.closest('.placeTemplate'),
                closeButton = '<div class="absolute cursor-pointer p-2 text-sm right-4 top-2.5 bg-[#ffffff] text-[#000000]" onclick="uiLibrary.closeCodeViewer()"><svg xmlns="http://www.w3.org/2000/svg" x="0px" y="0px" width="12" height="12" viewBox="0 0 50 50"><path d="M 9.15625 6.3125 L 6.3125 9.15625 L 22.15625 25 L 6.21875 40.96875 L 9.03125 43.78125 L 25 27.84375 L 40.9375 43.78125 L 43.78125 40.9375 L 27.84375 25 L 43.6875 9.15625 L 40.84375 6.3125 L 25 22.15625 Z" fill="#000000"></path></svg></div>',
                copyButton = '<div class="absolute cursor-pointer left-[50%] -top-5 bg-[#ffffff] text-[#000000] border-4 border-primary flex justify-center items-center text-sm rounded-full w-8 h-8 -ml-4 hidden">copy</div>';
            button.classList.add('activeCodeView');
            document.getElementById('codeViewer').innerHTML = '<div class="h-full pt-4 bg-[#f5f2f0]"><pre class="h-full"><code class="language-html">' + content.replace(/&/g, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;') + '</code></pre></div>' + copyButton + closeButton;
            Prism.highlightElement(document.getElementById('codeViewer').querySelectorAll('code')[0]);
            document.getElementById('pageOverlay').classList.remove('hidden');
            context.classList.add('activeElement', 'relative', 'z-[100]');
            document.getElementById('codeViewer').classList.remove('hidden');
        }
    },
    buildContentIndex: () => {
        uiLibrary.removeElementsByClass('removePlaceTemplate');
    },
    closeCodeViewer: () => {
        document.getElementById('codeViewer').classList.add('hidden');
        document.getElementsByClassName('activeCodeView')[0].classList.remove('activeCodeView');
        document.getElementById('pageOverlay').classList.add('hidden');
        document.getElementsByClassName('activeElement')[0].classList.remove('activeElement', 'relative', 'z-[100]');
    },
    removeElementsByClass: (className) => {
        const elements = document.getElementsByClassName(className);
        while(elements.length > 0){
            elements[0].parentNode.removeChild(elements[0]);
        }
    },
    doMore: () => {
        let placeTemplateDivs = document.getElementsByClassName('placeTemplate');

        for (let i = 0;i < placeTemplateDivs.length;i++) {
            let placeContext = placeTemplateDivs[i],
                templateName = placeContext.getAttribute('data-template'),
                templateNode = document.getElementById(templateName),
                componentTitle = templateNode.getAttribute('data-component-title'),
                componentWidth = 'fullscreen',
                theTitle = '',
                placeContextChildren = '';
            if (componentTitle === null) {
                componentTitle = placeContext.getAttribute('data-component-title');
            }
            if (templateNode !== null) {
                componentWidth = templateNode.dataset.componentWidth;
                theTitle = '';
                placeContextChildren = placeContext.innerHTML;
            }
            if (placeContext.dataset.displayCode !== '0') {
                theTitle = '<div class="text-[14px] mb-12 mt-6"><div class="rounded-sm inline-flex bg-[rgba(0,0,0,25%)] hover:bg-[rgba(14,159,110,80%)] cursor-pointer" onclick="uiLibrary.showCodePreview(\'' + templateName + '\', this)"><div class="font-medium leading-extrasmall m-1 inline-block px-2 py-1">' + componentTitle + '</div><div class="h-full m-1 ml-0 p-1 inline-block bg-[#dddddd]"><svg width="18px" height="18px"><use href="#code"/></svg></div></div></div>';
                if (componentWidth === 'fullscreen') {
                    theTitle = '<div class="corporate_grid"><div class="corporate_grid_full">' + theTitle + '</div></div>';
                }
                placeContext.innerHTML =  uiLibrary.getTemplateContent(templateName) + theTitle;
                placeContext.innerHTML = placeContext.innerHTML.replace('###content###', placeContextChildren);
            } else {
                let theNewContent = uiLibrary.getTemplateContent(templateName) + '<span class="removePlaceTemplate placeTemplate"></span>';
                theNewContent = theNewContent.replace('###content###', placeContextChildren);
                placeContext.outerHTML = theNewContent;
            }
        }

        for (let i = 0;i < document.getElementsByClassName('corporate_section').length;i++) {
            let section = document.getElementsByClassName('corporate_section')[i],
                sectionInfo = JSON.parse(section.dataset.sectionInfo),
                sectionHeader = document.createElement('div'),
                draftNote = ' <span class="bg-green text-white text-extrasmall inline-block px-1 pt-1 leading-extrasmall">Final</span>';
            if (sectionInfo.status === 'draft') {
                draftNote = ' <span class="bg-red text-white text-extrasmall inline-block px-1 pt-1 leading-extrasmall">Draft</span>';
            }

            sectionHeader.classList.add('placeHeaderContext');
            sectionHeader.innerHTML = uiLibrary.getTemplateContent('sectionHeader');
            sectionHeader.innerHTML = sectionHeader.innerHTML.replace('###title###', sectionInfo.title);
            sectionHeader.innerHTML = sectionHeader.innerHTML.replace('###description###', sectionInfo.description);
            sectionHeader.innerHTML = sectionHeader.innerHTML.replace('###version###', sectionInfo.version);

            section.prepend(sectionHeader);
            section.querySelectorAll('.placeHeaderContext')[0].outerHTML = section.querySelectorAll('.placeHeaderContext')[0].innerHTML;

            document.getElementById('contentIndex').innerHTML = document.getElementById('contentIndex').innerHTML + '<div><a class="inline-flex flex-row items-center gap-4 text-xl px-2 py-1 text-black items-center mb-4 bg-oxygenWhite font-tktypeBold" href="#' + section.getAttribute('id') + '">' + sectionInfo.title + draftNote + '</a></div>';
        }

        // init images with vanillaFocus
        let myImages = new vanillafocus({
            selector: ".vf-container",
            reCalcOnWindowResize: true
        });

        accordeon();
        highlightHeadlines();
        dropdown();
        tabs();

    }
}

uiLibrary.importTheTemplates(() => {
    uiLibrary.doMore();
    uiLibrary.buildContentIndex();

    function scrollTo(targetId) {
        let target = document.querySelectorAll(targetId)[0];
        window.setTimeout(() => {
            target.scrollIntoView({
                top: target.offsetTop,
                left: 0
            });
        }, 500);
    }
    if (window.location.hash) {
        scrollTo(window.location.hash);
    }
});
