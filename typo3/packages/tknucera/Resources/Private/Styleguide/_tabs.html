<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cards</title>
</head>
<body>

<template id="tabs">
<div class="w-full relative flex items-center ">
  <!-- Arrows -->
  <button id="tab-prev"
    class="absolute left-0 top-1/2 -translate-y-1/2 bg-gray-700 text-white px-2 py-1 rounded-l z-10 disabled:opacity-50">
    ◀
  </button>
  <button id="tab-next"
    class="absolute right-0 top-1/2 -translate-y-1/2 bg-gray-700 text-white px-2 py-1 rounded-r z-10 disabled:opacity-50">
    ▶
  </button>

  <!-- Tabs Wrapper -->
  <div id="tablist-wrapper" class="overflow-hidden w-full md:w-[unset]">
    <div
      id="tablist"
      role="tablist"
      aria-label="Sample Tabs"
      class="flex flex-row scroll-smooth transition-all duration-300"
    >
      <button
        role="tab"
        aria-selected="true"
        aria-controls="tabpanel-1"
        id="tab-1"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex flex-1 justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white">
        Item 1
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-2"
        id="tab-2"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex flex-1 justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white">
        Item 2
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-3"
        id="tab-3"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex flex-1 justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white">
        Item 3
      </button>
      <button
        role="tab"
        aria-selected="false"
        aria-controls="tabpanel-4"
        id="tab-4"
        class="px-4 py-2.5 text-skyGray text-small font-TKTypeMedium flex flex-1 justify-center bg-darkmetalGray border-b-2 border-skyGray hover:border-white">
        Item 4
      </button>
    </div>
  </div>
</div>
<!-- Panels -->
<div class="bg-darkmetalGray p-4">
  <div
    role="tabpanel"
    id="tabpanel-1"
    aria-labelledby="tab-1"
    tabindex="0"
    class="focus:outline-none text-white">
    <p>This is the content of Tab One.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-2"
    aria-labelledby="tab-2"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Two.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-3"
    aria-labelledby="tab-3"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Three.</p>
  </div>
  <div
    role="tabpanel"
    id="tabpanel-4"
    aria-labelledby="tab-4"
    tabindex="0"
    class="focus:outline-none text-white hidden">
    <p>This is the content of Tab Four.</p>
  </div>
</div>
</template>
</body>
</html>
