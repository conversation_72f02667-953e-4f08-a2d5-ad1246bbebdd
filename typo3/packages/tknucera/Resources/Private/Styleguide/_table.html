<!doctype html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>cards</title>
</head>
<body>

<template id="table">
  <table tabindex='1' class="hidden lg:table w-full border-collapse text-left" aria-label="Table example" role="table">
    <thead class="border-b-2 border-nuceraPurple">
      <tr>
        <th scope="col" class="text-nuceraPurple py-6 px-4">Name</th>
        <th scope="col" class="text-nuceraPurple py-6 px-4">Datum</th>
        <th scope="col" class="text-nuceraPurple py-6 px-4">Präsentation</th>
        <th scope="col" class="text-nuceraPurple py-6 px-4">Corporate News</th>
        <th scope="col" class="text-nuceraPurple py-6 px-4">Webcast</th>
        <th scope="col" class="text-nuceraPurple py-6 px-4"><PERSON><PERSON><PERSON><PERSON></th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
      <tr>
        <td class="py-6 px-4">Q2/H1-Ergebnis 2024/2025</td>
        <td class="py-6 px-4">15/05/2025</td>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
        <td class="py-6 px-4">
          <div class="flex flex-row gap-2 items-center">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB</td>
          </div>
      </tr>
    </tbody>
  </table>

<!-- Mobile table with full-width columns -->
<div class="lg:hidden overflow-x-auto w-full snap-x snap-mandatory">
  <table class="border-collapse text-left w-max" aria-label="Table example">
    <thead class="border-b-2 border-nuceraPurple">
      <tr>
        <!-- Each column takes full screen width -->
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q2/H1-Ergebnis 2024/2025</span><br>Name
        </th>
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q3/H2-Ergebnis 2024/2025</span><br>Name
        </th>
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q4/H2-Ergebnis 2024/2025</span><br>Name
        </th>
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q1/H1-Ergebnis 2025/2026</span><br>Name
        </th>
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q2/H1-Ergebnis 2025/2026</span><br>Name
        </th>
        <th class="text-nuceraPurple py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <span class="font-bold text-lg">Q3/H2-Ergebnis 2025/2026</span><br>Name
        </th>
      </tr>
    </thead>
    <tbody>
      <tr>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">15/05/2025<br>Datum</td>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">20/11/2025<br>Datum</td>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">10/02/2026<br>Datum</td>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">15/05/2026<br>Datum</td>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">20/08/2026<br>Datum</td>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">15/11/2026<br>Datum</td>
      </tr>
      <tr>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.15 MB
          </div>
          Präsentation
        </td>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 2.7 MB
          </div>
          Präsentation
        </td>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 4.1 MB
          </div>
          Präsentation
        </td>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 2.8 MB
          </div>
          Präsentation
        </td>
        <td class="py-6 px-4 bg-hydroGray-50 snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 3.9 MB
          </div>
          Präsentation
        </td>
        <td class="py-6 px-4 bg-white snap-start" style="width: 100vw;">
          <div class="flex items-center gap-2">
            <svg width="16" height="16"><use href="assets/images/spritemap.svg#picture-as-pdf"/></svg> Titel 1.5 MB
          </div>
          Präsentation
        </td>
      </tr>
    </tbody>
  </table>
</div>

</template>

</body>
</html>
