---
services:
  mariadb-{{.ENVIRONMENT}}:
    image: mariadb:10.5.23
    restart: always
    volumes:
      - mariadb-data:/var/lib/mysql
    environment:
      - MYSQL_ROOT_PASSWORD={{.db_rootUser_password}}
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio-{{.ENVIRONMENT}}:
    image: minio/minio
    command: server /data
    restart: always
    environment:
      - "MINIO_ROOT_USER={{.minio_root_user}}"
      - "MINIO_ROOT_PASSWORD={{.minio_root_password}}"
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.minio_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.middlewares.minio-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.minio-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.permanent=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}.middlewares=minio-{{.CI_COMMIT_REF_SLUG}}-redirect"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.minio_domain}}`)"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.tls.certresolver=letsencryptresolver"
      - "traefik.http.routers.minio-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=secHeaders@file"
    volumes:
      - ./data/minio:/data

  typo3-{{.ENVIRONMENT}}:
    image: {{.DOCKERIMAGE}}
    restart: always
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.rule=Host(`{{.typo3_domain}}`) || Host(`www.{{.typo3_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.entrypoints=web"
      - "traefik.http.middlewares.typo3-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.scheme=https"
      - "traefik.http.middlewares.typo3-{{.CI_COMMIT_REF_SLUG}}-redirect.redirectscheme.permanent=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}.middlewares=typo3-{{.CI_COMMIT_REF_SLUG}}-redirect"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.rule=Host(`{{.typo3_domain}}`) || Host(`www.{{.typo3_domain}}`)"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.entrypoints=websecure"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.tls=true"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.tls.certresolver=letsencryptresolver"
      - "traefik.http.routers.typo3-{{.CI_COMMIT_REF_SLUG}}secure.middlewares=secHeaders@file,nonWWWtoWWW,typo3-{{.CI_COMMIT_REF_SLUG}}-auth"
      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.regex=^https?://{{.typo3_domain}}(.*)"
      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.replacement=https://www.{{.typo3_domain}}$${1}"
      - "traefik.http.middlewares.nonWWWtoWWW.redirectregex.permanent=true"
      - "traefik.http.middlewares.typo3-{{.CI_COMMIT_REF_SLUG}}-auth.basicauth.users={{.basic_auth}}"
    environment:
      - MINIO_USER={{.minio_typo3_user}}
      - MINIO_PASSWORD={{.minio_typo3_password}}
      - MINIO_PUBLIC_URL={{.minio_domain}}/{{.minio_bucket}}/
      - MYSQL_HOST={{.db_host}}
      - MYSQL_DATABASE={{.db_database}}
      - MYSQL_USER={{.mariadb_id}}
      - MYSQL_PASSWORD={{.mariadb_password}}
      - TYPO3_CONTEXT={{.TYPO3_CONTEXT}}
      - SENTRY_RELEASE={{.GIT_TAG_SENTRY}}
    depends_on:
      mariadb-{{.ENVIRONMENT}}:
        condition: service_healthy
      minio-{{.ENVIRONMENT}}:
        condition: service_started
    logging:
      driver: "gelf"
      options:
        gelf-address: "udp://logs.mogic.com:12201"

volumes:
  mariadb-data:

networks:
  default:
    name: traefik
    external: true
