stages:
  - docker
  - preparation
  - testing
  - pushing
  - startVPN
  - pre-deploy
  - deploy
  - onSystem
  - onSystem-cleanup
  - stopVPN
  - manual
  - sbom

# Variables
variables:
  PRODUCTIONBRANCH: main
  TESTINGBRANCH: develop
  BASEIMAGE: gitlab-docker.mogic.com/docker/mogic-base-image:webapp-jammy-php8.2
  BUILDIMAGE: gitlab-docker.mogic.com/ascs/tknucera/frontend-assets:latest


.add_deploy_key: &add_deploy_key
  - 'which ssh-agent || ( apt-get update -y && apt-get install openssh-client git -y )'
  - eval $(ssh-agent -s)
  - DEPLOY_KEY=$(vault kv get -field=ssh-key-private devops/SSH/Mogic-General-Refresh)
  - echo "$DEPLOY_KEY" | tr -d '\r' | ssh-add - > /dev/null
  - mkdir -p ~/.ssh
  - chmod 700 ~/.ssh
  - ssh-keyscan gitlab.mogic.com >> ~/.ssh/known_hosts
  - chmod 644 ~/.ssh/known_hosts
  - if [ "$CI_COMMIT_REF_SLUG" == "$PRODUCTIONBRANCH" ];
      then export ENVIRONMENT='production'; export VAULTPATH_KV2="projects_prod/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME"; export VAULTPATH_KV1="projects_prod/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME";
      else export ENVIRONMENT='testing'; export VAULTPATH_KV2="projects/data/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; VAULTPATH_KV1="projects/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME/testing"; fi

################ Docker build section for images in this repo ######################################

.buildLocalDockerImages: &buildLocalDockerImages
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
  - docker run --rm --privileged multiarch/qemu-user-static --reset -p yes || true
  - docker context create mogic-context || true
  - docker buildx create mogic-context --name multiarch --driver docker-container --use || true
  - docker buildx build --no-cache --platform linux/amd64 --push -t gitlab-docker.mogic.com/ascs/tknucera/$IMAGENAME $DOCKERFILEPATH

.docker_build_template: &docker_build_template
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
    DOCKER_TLS_CERTDIR: "/certs"
  script:
    - *buildLocalDockerImages

build-frontend-assets:
  <<: *docker_build_template
  stage: docker
  variables:
    IMAGENAME: frontend-assets:latest
    DOCKERFILEPATH: ./docker/build-frontend-assets/
  only:
    changes:
      - docker/build-frontend-assets/**/*
    refs:
      - develop

###################################################################################################

composer:
  stage: preparation
  image: $BASEIMAGE
  script:
    - cd typo3/ && composer2 install
  artifacts:
    paths:
      - ./typo3/vendor/
      - ./typo3/public/
      - ./typo3/public/_assets/
    expire_in: 1 days
    when: always
  cache:
    paths:
      - ./typo3/vendor/
      - ./typo3/public/_assets/
  only:
    - branches

assets:
  stage: preparation
  image: $BUILDIMAGE
  script:
    - cd typo3/packages/tknucera/ && source ~/.profile; nvm install && yarn install && yarn build-prod
  artifacts:
    paths:
      - ./typo3/packages/tknucera/node_modules/
      - ./typo3/packages/tknucera/Resources/Public/
    expire_in: 1 days
    when: always
  cache:
    paths:
      - ./typo3/packages/tknucera/node_modules/
  only:
    - branches


# test-php:
#   stage: testing
#   image: $BASEIMAGE
#   before_script: *add_deploy_key
#   script:
#     - cd typo3 && ./vendor/bin/php-cs-fixer check --diff
#   dependencies:
#     - composer
#     # - assets
#   only:
#     - branches

# test-html:
#   stage: testing
#   image: $BUILDIMAGE
#   script:
#     - cd typo3/packages/tknucera && make local-checkstyle
#   only:
#     - branches

.push-script: &push-script
  - export GIT_TAG_SENTRY=$(git describe --tags --always)
  # Login to Docker-Registry
  - docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com
  - gitlabci-templating -inputTemplate ./template-data/ci/site-tknucera-template -vaultPath $VAULTPATH_KV2 -outputFile ./docker/typo3/conf/nginx/site-tknucera
  # Build and Push Dockerimage
  - docker pull $BASEIMAGE
  - docker build --build-arg BASEIMAGE=$BASEIMAGE -f docker/typo3/Dockerfile -t gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG} .
  - docker push gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  # Replace Variables in docker-compose-file (environment-related)
  - export DOCKERIMAGE=gitlab-docker.mogic.com/$CI_PROJECT_NAMESPACE/$CI_PROJECT_NAME:${CI_COMMIT_REF_SLUG}
  - gitlabci-templating -inputTemplate ./template-data/ci/docker-compose-template-$ENVIRONMENT -vaultPath $VAULTPATH_KV2 -outputFile $CI_PROJECT_DIR/docker-compose.yml

.deploy_script: &deploy_script
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  - ssh root@$TARGET_HOST "mkdir -p /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - rsync -havz --delete -e 'ssh -p 22' docker-compose.yml "root@$TARGET_HOST:/opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME"
  - ssh root@$TARGET_HOST "docker login -u gitlab-ci-token -p $CI_JOB_TOKEN gitlab-docker.mogic.com"
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose pull"
  - INSTANCES=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose ps -q typo3-$ENVIRONMENT") || true
  - if [[ -z $INSTANCES ]]; then ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --force-recreate"; exit 0; fi;
  - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
  - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
  - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; fi
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose up -d --no-recreate --scale typo3-$ENVIRONMENT=2";

buildAndPushTesting:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production/Testing
  script: *push-script
  dependencies:
    - composer
    - assets
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

buildAndPushProduction:
  stage: pushing
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  variables:
    DOCKER_DRIVER: overlay2
    TYPO3_CONTEXT: Production
  script: *push-script
  dependencies:
    - composer
    # - assets
  artifacts:
    paths:
      - docker-compose.yml
    expire_in: 1 days
    when: always
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

onSystem-stopCron:
  stage: pre-deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID sh -c 'supervisorctl stop cron'"
  allow_failure: true
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

deploy_testing:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  script: *deploy_script
  dependencies:
    - buildAndPushTesting
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://develop.tknucera.asnewbusiness.com
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

deploy_production:
  stage: deploy
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  variables:
    DOCKER_DRIVER: overlay2
  before_script: *add_deploy_key
  script: *deploy_script
  dependencies:
    - buildAndPushProduction
  environment:
    name: review/$CI_COMMIT_REF_NAME
    url: https://tknucera.asnewbusiness.com
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

onSystem-permissions-and-db:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID sh -c 'chown -R www-data:www-data /var/www/'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema safe'"
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 install:fixfolderstructure'"
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

onSystem-clearCaches:
  stage: onSystem
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    # change permissions on system
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - ssh root@$TARGET_HOST "docker exec -t $NEW_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

onSystem-cleanupOldInstance:
  stage: onSystem-cleanup
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - NEW_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - OLD_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created < .[1].Created then .[0].Id else if .[1].Id != "null" then .[1].Id else .[0].Id end end'")
    - if [[ $NEW_INSTANCE_ID != $OLD_INSTANCE_ID ]]; then ssh root@$TARGET_HOST "docker stop $OLD_INSTANCE_ID && docker rm $OLD_INSTANCE_ID"; else echo 'There is just one running instance, did nothing'; fi
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

overwrite-with-master-data:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script:
    # Mysql
    - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
    - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
    - export MINIO_DUMP_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
    - export MINIO_DUMP_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
    - export MINIO_DUMP_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
    - MARIADB_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q mariadb-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
    - TYPO3_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q typo3-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")

    - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD" || true
    - mc alias set mogic-minio $MINIO_DUMP_DOMAIN $MINIO_DUMP_USER $MINIO_DUMP_PASSWORD || true
    # List all files in the minio bucket
    - files=($(mc find "mogic-minio/tknucera-dumps/typo3/production/"))
    # Find the newest file
    - newest_file=${files[0]}
    - |+
      for file in "${files[@]}"; do
        if [[ "$file" > "$newest_file" ]]; then
          newest_file="$file"
        fi
      done
    - chosen_file="$newest_file"
    - ssh root@$TARGET_HOST "mc cp $chosen_file /tmp/datadump_tknucera.sql.gz"
    - ssh root@$TARGET_HOST "docker cp /tmp/datadump_tknucera.sql.gz $MARIADB_INSTANCE_ID:/tmp/datadump_tknucera.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$ENVIRONMENT sh -c 'zcat /tmp/datadump_tknucera.sql.gz | mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE"'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 database:updateschema'"
    - rsync -avz data/mariadb/local-storage.sql root@$TARGET_HOST:/tmp/local-storage.sql
    - ssh root@$TARGET_HOST "docker cp /tmp/local-storage.sql $MARIADB_INSTANCE_ID:/tmp/local-storage.sql"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$ENVIRONMENT sh -c ' mysql -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE" < /tmp/local-storage.sql'"
    - ssh root@$TARGET_HOST "docker exec -t $TYPO3_INSTANCE_ID su www-data -s /bin/bash -c './vendor/bin/typo3 cache:flush'"
    - ssh root@$TARGET_HOST "rm /tmp/datadump_tknucera.sql.gz"
    - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$ENVIRONMENT sh -c 'rm /tmp/datadump_tknucera.sql.gz'"
    - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true
  when: manual
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $TESTINGBRANCH

.create_data_dump: &create_data_dump
  - export TARGET_HOST=$(vault kv get -field="TARGET_HOST" $VAULTPATH_KV1)
  - ssh-keyscan $TARGET_HOST >> ~/.ssh/known_hosts
  # get db dump gzip file
  - MARIADB_INSTANCE_ID=$(ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker inspect \$(docker-compose ps -q mariadb-$ENVIRONMENT) | jq 'if .[0].Created > .[1].Created then .[0].Id else .[1].Id end'")
  - DATE=$(date '+%Y-%m-%d_%H-%M-%S')
  - DUMP_FILE_NAME=$DATE-typo3.sql
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$ENVIRONMENT sh -c ' mysqldump -u"\$MYSQL_USER" -p"\$MYSQL_PASSWORD" "\$MYSQL_DATABASE" | gzip > /tmp/$DUMP_FILE_NAME.gz'"
  - ssh root@$TARGET_HOST "docker cp $MARIADB_INSTANCE_ID:/tmp/$DUMP_FILE_NAME.gz /tmp/$DUMP_FILE_NAME.gz"
  # get minio connection parameters
  - export MINIO_WRITE_USER=$(vault kv get -field="db-dumps-id" $VAULTPATH_KV1)
  - export MINIO_WRITE_PASSWORD=$(vault kv get -field="db-dumps-password" $VAULTPATH_KV1)
  - export MINIO_DOMAIN=$(vault kv get -field="db-dumps_login_url" $VAULTPATH_KV1)
  # create minio connection
  - ssh root@$TARGET_HOST "mc alias set mogic-minio $MINIO_DOMAIN $MINIO_WRITE_USER $MINIO_WRITE_PASSWORD" || true
  # sync db dump file to minio
  - ssh root@$TARGET_HOST "mc cp /tmp/$DUMP_FILE_NAME.gz mogic-minio/tknucera-dumps/typo3/$ENVIRONMENT/$DUMP_FILE_NAME.gz"
  # cleanup dump files from host
  - ssh root@$TARGET_HOST "rm /tmp/$DUMP_FILE_NAME.gz"
  - ssh root@$TARGET_HOST "cd /opt/$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-$CI_COMMIT_REF_NAME/ && docker-compose exec -T mariadb-$ENVIRONMENT sh -c ' rm /tmp/$DUMP_FILE_NAME.gz'"
  - ssh root@$TARGET_HOST "mc alias remove mogic-minio" || true

create-prod-data_dump:
  stage: manual
  image: gitlab-docker.mogic.com/docker/gitlab-ci-git-dind
  before_script: *add_deploy_key
  script: *create_data_dump
  when: manual
  dependencies: []
  only:
    variables:
      - $CI_COMMIT_REF_SLUG == $PRODUCTIONBRANCH

generate-sbom:
  stage: sbom
  image: $BASEIMAGE
  before_script: *add_deploy_key
  script:
    - composer2 global remove hirak/prestissimo || true
    - composer2 global config --no-plugins allow-plugins.cyclonedx/cyclonedx-php-composer true || true
    - composer2 global require cyclonedx/cyclonedx-php-composer
    - cd typo3 && composer2 make-bom || composer2 CycloneDX:make-sbom  --output-file=bom.xml
    - DEPTRACK_URL=$(vault kv get -field="deptrack_api_url" devops/Dependencytrack)
    - DEPTRACK_API_KEY=$(vault kv get -field="deptrack_api_key" devops/Dependencytrack)
    - "curl --fail-with-body -X POST $DEPTRACK_URL -H \"Content-Type: multipart/form-data\" -H \"X-API-Key: $DEPTRACK_API_KEY\" -F \"autoCreate=true\" -F \"projectName=$CI_PROJECT_NAMESPACE-$CI_PROJECT_NAME-composer\" -F \"projectVersion=$CI_COMMIT_TAG\" -F bom=@bom.xml"
  dependencies: []
  only:
    - tags
