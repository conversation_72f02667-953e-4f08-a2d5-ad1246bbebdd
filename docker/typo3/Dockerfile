FROM gitlab-docker.mogic.com/docker/mogic-base-image:webapp-jammy-php8.2

ADD docker/typo3/conf /etc/mogic

RUN apt-get update\
    && apt-get install -qqy --no-install-recommends\
        ghostscript\
    && apt autoremove -y && apt clean -y && rm -rf /var/lib/apt/lists/* /tmp/* /var/tmp/*\
    && ln -sf /etc/mogic/php/90-tknucera.ini /etc/php/8.2/fpm/conf.d/\
    && ln -sf /etc/mogic/php/90-tknucera.ini /etc/php/8.2/cli/conf.d/\
    && ln -sf /etc/mogic/php/fpm-tknucera.conf /etc/php/8.2/fpm/pool.d/tknucera.conf\
    && ln -sf /etc/mogic/php/fpm-www.conf /etc/php/8.2/fpm/pool.d/www.conf\
    && ln -sf /etc/mogic/nginx/site-tknucera /etc/nginx/sites-enabled/tknucera\
    && rm /etc/nginx/sites-enabled/default\
    \
    && ln -sf /etc/mogic/nginx/nginx.conf /etc/nginx/\
    && ln -sf /etc/mogic/nginx/fastcgi_params-extra /etc/nginx/\
    && cp /etc/mogic/cron/* /etc/cron.d/\
    && cp /etc/mogic/setup.sh /root/setup.sh && chmod +x /root/setup.sh\
    \
    && rm /etc/supervisor/conf.d/cront.conf || true\
    && ln -s /etc/mogic/supervisor/conf.d/cront.conf /etc/supervisor/conf.d/cront.conf\
    \
    && chown -R www-data:www-data /var/www

COPY --chown=www-data:www-data ./typo3/ /var/www/typo3/

VOLUME ["/var/www/typo3"]
WORKDIR /var/www/typo3/
