; thread pool "www"
[www]
user = www-data
group = www-data

listen = /run/php/php8.2-fpm.sock

listen.owner = www-data
listen.group = www-data

request_slowlog_timeout = 0

pm = dynamic
pm.max_children = 20
pm.start_servers = 3
pm.min_spare_servers = 2
pm.max_spare_servers = 4
pm.max_requests = 500

request_terminate_timeout = 120s

rlimit_files = 131072
rlimit_core = unlimited

; Ensure worker stdout and stderr are sent to the main error log.
catch_workers_output = yes

; Make environment variables available to php
clear_env = no

; Do not send errors to nginx. supervisor already captures fpm errors.
php_admin_flag[fastcgi.logging] = off
