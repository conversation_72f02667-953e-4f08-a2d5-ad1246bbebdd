---
services:

  mariadb:
    image: mariadb:10.5.23
    ports:
      - "3424:3306"
    volumes:
      - mariadb-data:/var/lib/mysql
    networks:
      tknucera_network:
        aliases:
          - mariadb
    env_file:
      - env-mariadb.ini
    healthcheck:
      test: ["CMD", "healthcheck.sh", "--su-mysql", "--connect"]
      interval: 1s
      timeout: 5s
      retries: 90

  minio:
    image: minio/minio
    command: server /data
    ports:
      - "9084:9000"
    volumes:
      - ./data/minio:/data
    networks:
      tknucera_network:
        aliases:
          - minio

  typo3:
    build: docker/typo3-dev/
    volumes:
      - ./typo3:/var/www/typo3/
    ports:
      - "5684:80"
    environment:
      - MINIO_PROD_USER=typo3-readonly
      - MINIO_PROD_PASSWORD=O4wH4AqLY0Gx8MRpR4gvxBZrtp2xQeT86ScjdeFy
      - MINIO_PROD_CUSTOM_HOST=https://files.tknucera.asnewbusiness.com
      - MINIO_PROD_PUBLIC_URL=files.tknucera.asnewbusiness.com/tknucera-typo3/
      - MINIO_LOCAL_USER=typo3-dev
      - MINIO_LOCAL_PASSWORD=kqQPp97s8R2ScUAzxYbyVj
      - MINIO_LOCAL_PUBLIC_URL=files.tknucera.test:9084/tknucera-typo3/
      - MINIO_LOCAL_CUSTOM_HOST=http://minio:9000
      - TYPO3_CONTEXT=Development/Local
      - SENTRY_RELEASE=develop
    networks:
      tknucera_network:
        aliases:
          - tknucera.test
    depends_on:
      mariadb:
        condition: service_healthy
      minio:
        condition: service_started

  build:
    build: docker/build-frontend-assets
    volumes:
      - ./typo3/packages/tknucera:/var/www
    restart: "no"
    profiles:
      - tools

  sync-database:
    build: docker/sync-database
    volumes:
      - .:/data
      - ./docker/sync-database/syncDatabase.sh:/syncDatabase.sh
    env_file:
      - env-mariadb.ini
    environment:
      - BUCKET_NAME=tknucera-dumps
      - DIRECTORY_PATH=typo3/production
    depends_on:
      mariadb:
        condition: service_healthy
    networks:
      tknucera_network:
    restart: "no"
    profiles:
      - tools

volumes:
  mariadb-data:

networks:
  tknucera_network:
    name: tknucera
