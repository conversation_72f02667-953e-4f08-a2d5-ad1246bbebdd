tknucera Website
************************


URLs
====

Testsystem

- Frontend: https://develop.tknucera.asnewbusiness.com/
- Backend: https://develop.tknucera.asnewbusiness.com/typo3/

Lokales Entwicklungssystem:

- Frontend: http://tknucera.test:5684/
- Backend: http://tknucera.test:5684/typo3/

  - Nutzer: admin87dbi
  - Passwort: https://keepersecurity.eu/vault/#detail/UGwWhYZ4f48SUJ9HZ2F5CA


Setup
=====
Einträge in die ``/etc/hosts``::

  127.0.0.1 tknucera.test files.tknucera.test
  ::1       tknucera.test files.tknucera.test

Initiales Setup::

  $ <NAME_EMAIL>:ascs/tknucera.git
  $ make up-new-keep-sources

Hier wird man nach Zugangsdaten für S3 gefragt.
Diese sind zu finden unter https://service.mogic.com/user/profile/minio


Start
=====
Wenn man schonmal aufgesetzt hat::

  $ make start

